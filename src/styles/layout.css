/**
 * Layout 相关的样式定义
 * 主要用于 React Flow 节点的样式定制
 */

/* 空占位符节点样式 */
.empty-placeholder-node {
  /* 基础样式 */
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

/* 亮色主题 */
.empty-placeholder-node {
  background-color: #f8fafc; /* slate-50 */
  border: 2px dashed #cbd5e1; /* slate-300 */
  color: #64748b; /* slate-500 */
}

/* 暗色主题 */
.dark .empty-placeholder-node {
  background-color: #334155; /* slate-700 */
  border: 2px dashed #64748b; /* slate-500 */
  color: #cbd5e1; /* slate-300 */
}

/* 悬停效果 */
.empty-placeholder-node:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .empty-placeholder-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
