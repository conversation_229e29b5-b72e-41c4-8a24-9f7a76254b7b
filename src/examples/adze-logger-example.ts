/**
 * Adze Logger 使用示例
 * 
 * 这个文件展示了如何在 RegexAI 项目中使用 Adze 日志库
 */

import { adzeLog } from '../lib/adze-logger';

// 基础日志示例
export function basicLoggingExample() {
  console.log('\n=== 基础日志示例 ===');
  
  adzeLog.debug('这是一个调试信息', { userId: 123 });
  adzeLog.info('应用启动成功', { version: '1.0.0' });
  adzeLog.warn('这是一个警告信息', { component: 'RegexParser' });
  adzeLog.error('这是一个错误信息', new Error('示例错误'));
  adzeLog.success('操作成功完成');
  adzeLog.fail('操作失败');
}

// API 日志示例
export function apiLoggingExample() {
  console.log('\n=== API 日志示例 ===');
  
  // 模拟 API 请求
  adzeLog.api.request('POST', '/api/regex/parse', { pattern: '/test/g' });
  
  // 模拟成功响应
  adzeLog.api.response('POST', '/api/regex/parse', 200, 45, { 
    result: 'success',
    nodesCount: 5 
  });
  
  // 模拟错误响应
  adzeLog.api.response('POST', '/api/regex/validate', 400, 12, { 
    error: 'Invalid regex pattern' 
  });
  
  // 模拟 API 错误
  adzeLog.api.error('GET', '/api/regex/history', new Error('Database connection failed'));
}

// 性能日志示例
export function performanceLoggingExample() {
  console.log('\n=== 性能日志示例 ===');
  
  // 方式1：使用 start/end 模式
  const timer = adzeLog.performance.start('正则表达式解析');
  
  // 模拟一些处理时间
  setTimeout(() => {
    const duration = timer.end({ pattern: '/complex.*regex/gi', nodesGenerated: 15 });
    console.log(`操作耗时: ${duration.toFixed(2)}ms`);
  }, 100);
  
  // 方式2：直接记录已知的耗时
  adzeLog.performance.measure('图谱渲染', 234.56, { 
    nodeCount: 20,
    edgeCount: 35 
  });
}

// UI 交互日志示例
export function uiLoggingExample() {
  console.log('\n=== UI 交互日志示例 ===');
  
  adzeLog.ui.click('regex-input-button', { position: { x: 100, y: 200 } });
  adzeLog.ui.input('regex-pattern', '/test/g', { length: 6 });
  adzeLog.ui.navigation('home', 'regex-editor', { userId: 123 });
  adzeLog.ui.error('RegexVisualization', new Error('Canvas rendering failed'), {
    canvasSize: { width: 800, height: 600 }
  });
}

// 正则表达式处理日志示例
export function regexLoggingExample() {
  console.log('\n=== 正则表达式处理日志示例 ===');
  
  // 成功解析
  adzeLog.regex.parse('/hello.*world/gi', true, { 
    flags: ['g', 'i'],
    complexity: 'medium' 
  });
  
  // 解析失败
  adzeLog.regex.parse('/[unclosed', false, { 
    error: 'Unclosed character class' 
  });
  
  // 匹配结果
  adzeLog.regex.match('/test/g', 'test string test', 2, {
    matches: ['test', 'test'],
    positions: [0, 12]
  });
  
  // 正则表达式错误
  adzeLog.regex.error('/(?<invalid/g', new Error('Invalid named capture group'), {
    position: 9
  });
}

// 综合示例：模拟一个完整的正则表达式处理流程
export function comprehensiveExample() {
  console.log('\n=== 综合示例：正则表达式处理流程 ===');
  
  const regexPattern = '/hello\\s+(world|universe)/gi';
  const testText = 'Hello world! Hello universe!';
  
  // 1. 用户输入
  adzeLog.ui.input('regex-pattern-input', regexPattern);
  adzeLog.ui.input('test-text-input', testText);
  
  // 2. 开始处理
  const parseTimer = adzeLog.performance.start('正则表达式完整处理');
  
  // 3. 解析正则表达式
  adzeLog.regex.parse(regexPattern, true, { 
    flags: ['g', 'i'],
    hasGroups: true,
    complexity: 'medium'
  });
  
  // 4. 执行匹配
  adzeLog.regex.match(regexPattern, testText, 2, {
    matches: ['Hello world', 'Hello universe'],
    groups: [['world'], ['universe']]
  });
  
  // 5. 生成可视化
  adzeLog.performance.measure('图谱节点生成', 45.2, { nodeCount: 8 });
  adzeLog.performance.measure('图谱布局计算', 23.7, { algorithm: 'elkjs' });
  adzeLog.performance.measure('Canvas 渲染', 67.3, { 
    canvasSize: { width: 1200, height: 800 }
  });
  
  // 6. 完成处理
  parseTimer.end({ 
    totalNodes: 8,
    totalMatches: 2,
    success: true 
  });
  
  adzeLog.success('正则表达式处理完成', {
    pattern: regexPattern,
    matchCount: 2,
    processingTime: '136.2ms'
  });
}

// 错误处理示例
export function errorHandlingExample() {
  console.log('\n=== 错误处理示例 ===');
  
  try {
    // 模拟一个可能出错的操作
    throw new Error('模拟的处理错误');
  } catch (error) {
    adzeLog.error('处理正则表达式时发生错误', error as Error, {
      pattern: '/invalid[/g',
      context: 'user-input-validation',
      timestamp: new Date().toISOString()
    });
  }
  
  // 记录警告
  adzeLog.warn('检测到复杂的正则表达式', {
    pattern: '/(?:(?:(?:[a-z]+){2,}){3,}){4,}/g',
    complexity: 'very-high',
    recommendation: '考虑简化表达式以提高性能'
  });
}

// 运行所有示例（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  console.log('🚀 Adze Logger 示例演示');
  
  basicLoggingExample();
  apiLoggingExample();
  performanceLoggingExample();
  uiLoggingExample();
  regexLoggingExample();
  comprehensiveExample();
  errorHandlingExample();
  
  console.log('\n✅ 所有示例演示完成');
}
