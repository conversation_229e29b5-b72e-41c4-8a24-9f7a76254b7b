/**
 * 图形相关的统一类型定义
 * 避免在不同文件中重复定义相同的接口
 */

import type { Node, Edge } from '@xyflow/react'
import type { ElkNode } from 'elkjs'
import type { AstNode } from 'regexp-tree/ast'

/**
 * 节点类型枚举 - 统一定义
 */
export enum NodeType {
  SEQUENCE = 'sequence',
  ALTERNATION = 'alternation',
  QUANTIFIER = 'quantifier',
  CHARACTER_CLASS = 'charClass',
  GROUP = 'group',
  ASSERTION = 'assertion',
  LITERAL = 'literal',
  ROOT = 'root',
  MACRO_SEQUENCE = 'macro_sequence',  // 新增：宏观序列节点类型（UI 层需同步支持）
  JUNCTION = 'junction'  // 新增：分支汇合点节点类型
}

/**
 * 节点数据接口 - 统一定义
 * 用于GraphPanel和transformer等模块
 */
export interface NodeData extends Record<string, unknown> {
  /** 节点显示标签 */
  label?: string
  /** 对应的正则表达式片段，用于高亮和解释 */
  regexFragment?: string
  /** 节点的语义类型（避免与 React Flow 的 Node.type 混淆） */
  semanticType: NodeType
  /** 节点内容 */
  content: string
  /** 原始文本 */
  originalText: string
  /** AST节点类型 */
  astNodeType: string
}

/**
 * 扩展的React Flow节点类型
 */
export type GraphNode = Node<NodeData>

/**
 * 扩展的React Flow边类型
 */
export type GraphEdge = Edge

/**
 * 扩展的 ELK 节点接口，包含我们的自定义数据
 */
export interface CustomElkNode extends ElkNode {
  data?: NodeData;
}

/**
 * 图形布局结果类型
 */
export interface GraphLayoutResult {
  nodes: GraphNode[]
  edges: GraphEdge[]
}

/**
 * 自定义节点组件的Props类型
 */
export interface CustomNodeProps {
  id: string
  data: NodeData
}

/**
 * 宏观序列节点类型
 * 用于表示合并后的连续字符序列
 */
export interface MacroSequenceNode {
  type: 'MacroSequence'
  value: string
  // 保留位置信息等元数据
  loc?: any
  range?: any
}

/**
 * 扩展的 AST 节点类型，包含宏观序列节点
 * 这是我们内部使用的类型，兼容 regexp-tree 的 AstNode
 */
export type ExtendedAstNode = AstNode | MacroSequenceNode
