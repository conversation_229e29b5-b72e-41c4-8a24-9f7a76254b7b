/**
 * 全局类型定义
 * 消除 any 类型，提供严格的类型安全
 */

/**
 * 正则表达式解析结果类型
 */
export interface ParseRegexResult {
  ast: ASTNode | null;
  error: string | null;
  complexity: number;
  nodeCount: number;
}

/**
 * AST 节点基础类型
 */
export interface ASTNode {
  type: string;
  value?: string;
  children?: ASTNode[];
  quantifier?: Quantifier;
  flags?: string[];
  position?: NodePosition;
  id?: string;
}

/**
 * 节点位置信息
 */
export interface NodePosition {
  start: number;
  end: number;
  line?: number;
  column?: number;
}

/**
 * 量词类型
 */
export interface Quantifier {
  type: 'exact' | 'range' | 'star' | 'plus' | 'optional';
  min?: number;
  max?: number;
  greedy?: boolean;
}

/**
 * AI 解释缓存值类型
 */
export type AIExplanationValue = string;

/**
 * AI 解释缓存键类型
 */
export type AIExplanationKey = string;

/**
 * 布局元素结果类型
 */
export interface LayoutResult {
  nodes: LayoutNode[];
  edges: LayoutEdge[];
  bounds?: LayoutBounds;
}

/**
 * 布局节点类型
 */
export interface LayoutNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: NodeData;
  width?: number;
  height?: number;
}

/**
 * 布局边类型
 */
export interface LayoutEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  data?: EdgeData;
}

/**
 * 节点数据类型
 */
export interface NodeData {
  label: string;
  type: string;
  value?: string;
  description?: string;
  astNode?: ASTNode;
  isHighlighted?: boolean;
  hasError?: boolean;
}

/**
 * 边数据类型
 */
export interface EdgeData {
  label?: string;
  type?: string;
  animated?: boolean;
}

/**
 * 布局边界类型
 */
export interface LayoutBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * 存储快照类型
 */
export interface StoreSnapshot {
  regexString: string;
  testString: string;
  ast: ASTNode | null;
  flowNodes: LayoutNode[];
  flowEdges: LayoutEdge[];
  timestamp: number;
  version: number;
}

/**
 * 错误恢复状态类型
 */
export interface ErrorRecoveryState {
  canRecover: boolean;
  lastValidSnapshot: StoreSnapshot | null;
  errorCount: number;
  lastErrorTime: number;
}

/**
 * 性能指标详细类型
 */
export interface DetailedPerformanceMetrics {
  parseTime: number;
  layoutTime: number;
  totalTime: number;
  nodeCount: number;
  edgeCount: number;
  complexity: number;
  timestamp: number;
  regexLength: number;
  memoryUsage?: number;
  cacheHitRate?: number;
  apiCallCount?: number;
}

/**
 * 配置验证结果类型
 */
export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * API 响应基础类型
 */
export interface BaseAPIResponse {
  success: boolean;
  timestamp: number;
  requestId?: string;
}

/**
 * API 错误响应类型
 */
export interface APIErrorResponse extends BaseAPIResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
}

/**
 * API 成功响应类型
 */
export interface APISuccessResponse<T = unknown> extends BaseAPIResponse {
  success: true;
  data: T;
}

/**
 * 通用 API 响应类型
 */
export type APIResponse<T = unknown> = APISuccessResponse<T> | APIErrorResponse;

/**
 * 缓存统计信息类型
 */
export interface CacheStats {
  size: number;
  maxSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  oldestTimestamp?: number;
  newestTimestamp?: number;
}

/**
 * 内存使用统计类型
 */
export interface MemoryStats {
  currentUsage?: number;
  averageUsage?: number;
  peakUsage?: number;
  unit: 'bytes' | 'kb' | 'mb' | 'gb';
}

/**
 * 日志上下文类型
 */
export interface LogContext {
  [key: string]: unknown;
}

/**
 * 事件处理器类型
 */
export type EventHandler<T = unknown> = (event: T) => void | Promise<void>;

/**
 * 异步操作结果类型
 */
export type AsyncResult<T, E = Error> = Promise<
  | { success: true; data: T }
  | { success: false; error: E }
>;

/**
 * 可选字段类型工具（自定义名称避免与 TS 内置冲突）
 */
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 必需字段类型工具（自定义名称避免与 TS 内置冲突）
 */
export type MakeRequired<T, K extends keyof T> = T & { [P in K]-?: T[P] };

/**
 * 深度只读类型工具
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 函数参数类型提取工具
 */
export type ExtractFunctionArgs<T> = T extends (...args: infer A) => unknown ? A : never;

/**
 * 函数返回类型提取工具
 */
export type ExtractFunctionReturn<T> = T extends (...args: unknown[]) => infer R ? R : never;
