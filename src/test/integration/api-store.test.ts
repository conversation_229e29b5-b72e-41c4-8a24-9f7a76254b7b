import { describe, it, expect, beforeEach, afterAll, vi } from 'vitest';
import { useRegexStore } from '../../store/regex.store';
import { t } from '../../lib/i18n';
import { LRUCache } from '../../lib/lru-cache';
import { TEST_CONFIG } from '../../lib/config';

// 保存原始fetch以便恢复
const originalFetch = global.fetch;

// Mock fetch for testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('API Store Integration', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useRegexStore.setState({
      regexString: '',
      testString: '',
      ast: null,
      flowNodes: [],
      flowEdges: [],
      aiExplanations: new LRUCache<string, string>({ maxSize: 100, ttl: 3600000 }),
      hoveredElementId: null,
      error: null,
      isLoading: false,
      lastValidState: null,
      requestVersion: 0,
      pendingExplanations: new Set<string>(),
      pendingNodeIds: new Map<string, Set<string>>(),
      aiErrorNodeIds: new Set<string>(),
    });

    // Reset fetch mock
    mockFetch.mockReset();
  });

  // 恢复原始fetch，防止影响其他测试套件
  afterAll(() => {
    global.fetch = originalFetch;
  });

  describe('_fetchExplanationForNode', () => {
    it('should successfully fetch explanation from API', async () => {
      // Mock successful API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          explanation: '这是对 \\d+ 的一个模拟解释。'
        })
      });

      const store = useRegexStore.getState();
      
      // Call the method
      await store._fetchExplanationForNode('test-node-1', '\\d+');

      // Verify fetch was called with correct parameters
      expect(mockFetch).toHaveBeenCalledWith('/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ snippet: '\\d+' }),
      });

      // Verify explanation was stored
      const updatedState = useRegexStore.getState();
      expect(updatedState.aiExplanations.get('test-node-1')).toMatch(/模拟解释|mock.*explanation/i);
    });

    it('should not fetch if explanation already exists', async () => {
      // Pre-populate explanation
      useRegexStore.setState({
        aiExplanations: (() => {
          const cache = new LRUCache<string, string>({ maxSize: 100, ttl: 3600000 });
          cache.set('test-node-1', 'Existing explanation');
          return cache;
        })()
      });

      const store = useRegexStore.getState();
      
      // Call the method
      await store._fetchExplanationForNode('test-node-1', '\\d+');

      // Verify fetch was not called
      expect(mockFetch).not.toHaveBeenCalled();

      // Verify explanation remains unchanged
      const updatedState = useRegexStore.getState();
      expect(updatedState.aiExplanations.get('test-node-1')).toBe('Existing explanation');
    });

    it('should handle API errors with retry mechanism', async () => {
      // Mock API failure followed by success
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            explanation: '重试后成功的解释'
          })
        });

      const store = useRegexStore.getState();
      
      // Call the method
      await store._fetchExplanationForNode('test-node-2', '[a-z]+');

      // Verify fetch was called twice (initial + 1 retry)
      expect(mockFetch).toHaveBeenCalledTimes(2);

      // Verify explanation was eventually stored
      const updatedState = useRegexStore.getState();
      expect(updatedState.aiExplanations.get('test-node-2')).toMatch(/重试后成功|Mock explanation/i);
    });

    it('should handle HTTP error responses', async () => {
      // Mock HTTP error response for all 3 retry attempts
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 400,
          statusText: 'Bad Request'
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 400,
          statusText: 'Bad Request'
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 400,
          statusText: 'Bad Request'
        });

      const store = useRegexStore.getState();

      // Call the method
      await store._fetchExplanationForNode('test-node-3', 'invalid-regex');

      // Verify fetch was called
      expect(mockFetch).toHaveBeenCalledWith('/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ snippet: 'invalid-regex' }),
      });

      // Since it will retry 3 times, fetch should be called 3 times
      expect(mockFetch).toHaveBeenCalledTimes(3);

      // Verify error explanation was set using i18n
      const updatedState = useRegexStore.getState();
      expect(updatedState.aiExplanations.get('test-node-3')).toBe(t('errors.aiExplanationFailed'));
    }, Math.max(TEST_CONFIG.TIMEOUT_MS * 2.5, 25000)); // CI 友好：至少 25 秒

    it('should handle multiple concurrent requests for different nodes', async () => {
      // Mock successful responses for different nodes
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ explanation: 'Mock explanation 1' })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ explanation: 'Mock explanation 2' })
        });

      const store = useRegexStore.getState();
      
      // Call the method for multiple nodes concurrently
      await Promise.all([
        store._fetchExplanationForNode('node-1', '\\d+'),
        store._fetchExplanationForNode('node-2', '[a-z]+')
      ]);

      // Verify both explanations were stored
      const updatedState = useRegexStore.getState();
      expect(updatedState.aiExplanations.get('node-1')).toMatch(/Mock explanation/);
      expect(updatedState.aiExplanations.get('node-2')).toMatch(/Mock explanation/);
      expect(updatedState.aiExplanations.getSize()).toBe(2);
    });

    it('should handle complex regex patterns', async () => {
      const complexPattern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';
      
      // Mock successful API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          explanation: `这是对 '${complexPattern}' 的一个模拟解释。`
        })
      });

      const store = useRegexStore.getState();
      
      // Call the method
      await store._fetchExplanationForNode('email-regex', complexPattern);

      // Verify fetch was called with correct complex pattern
      expect(mockFetch).toHaveBeenCalledWith('/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ snippet: complexPattern }),
      });

      // Verify explanation was stored
      const updatedState = useRegexStore.getState();
      expect(updatedState.aiExplanations.get('email-regex')).toContain(complexPattern);
    });

    it('should handle 10+ concurrent requests with cache hits', async () => {
      // Mock successful API responses
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({ explanation: 'Cached explanation' })
      });

      const store = useRegexStore.getState();

      // First, populate cache with some entries
      await store._fetchExplanationForNode('cached-node', '\\d+');

      // Now make 10+ concurrent requests, some should hit cache
      const requests = [];
      for (let i = 0; i < 12; i++) {
        if (i % 3 === 0) {
          // Every 3rd request uses cached pattern
          requests.push(store._fetchExplanationForNode('cached-node', '\\d+'));
        } else {
          requests.push(store._fetchExplanationForNode(`node-${i}`, `pattern-${i}`));
        }
      }

      await Promise.all(requests);

      // Verify cache behavior
      const finalState = useRegexStore.getState();
      expect(finalState.aiExplanations.getSize()).toBeGreaterThan(8); // Should have multiple entries
      expect(finalState.aiExplanations.has('cached-node')).toBe(true);
    }, Math.max(TEST_CONFIG.TIMEOUT_MS * 3, 30000)); // CI 友好：至少 30 秒

    it('should test LRU cache eviction', async () => {
      // Mock successful API responses
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({ explanation: 'Test explanation' })
      });

      const store = useRegexStore.getState();

      // Fill cache beyond its limit (assuming limit is 100)
      const requests = [];
      for (let i = 0; i < 105; i++) {
        requests.push(store._fetchExplanationForNode(`node-${i}`, `pattern-${i}`));
      }

      await Promise.all(requests);

      const finalState = useRegexStore.getState();

      // Cache should not exceed its limit
      expect(finalState.aiExplanations.getSize()).toBeLessThanOrEqual(100);

      // Oldest entries should be evicted
      expect(finalState.aiExplanations.has('node-0')).toBe(false);
      expect(finalState.aiExplanations.has('node-104')).toBe(true);
    }, Math.max(TEST_CONFIG.TIMEOUT_MS * 4, 40000)); // CI 友好：至少 40 秒，LRU 测试需要更多时间

    it('should reuse explanations for duplicate snippets', async () => {
      // Mock successful API response
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ explanation: 'Shared explanation' })
      });

      const store = useRegexStore.getState();

      // First request should make API call
      await store._fetchExplanationForNode('node-a', '\\d+');

      // Second request with same node ID should hit cache
      await store._fetchExplanationForNode('node-a', '\\d+');

      // Third request with same node ID should also hit cache
      await store._fetchExplanationForNode('node-a', '\\d+');

      // Should only make one API call due to caching
      expect(mockFetch).toHaveBeenCalledTimes(1);

      const finalState = useRegexStore.getState();
      expect(finalState.aiExplanations.has('node-a')).toBe(true);
      expect(finalState.aiExplanations.get('node-a')).toBe('Shared explanation');
    });
  });
});
