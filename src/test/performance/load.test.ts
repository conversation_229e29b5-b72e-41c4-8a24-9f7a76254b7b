import { describe, it, expect, beforeEach, afterAll, vi } from 'vitest';
import { useRegexStore } from '../../store/regex.store';
import { TEST_CONFIG } from '../../lib/config';
import { LRUCache } from '../../lib/lru-cache';

// 保存原始fetch以便恢复
const originalFetch = global.fetch;

// Mock fetch for testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('Load Testing', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useRegexStore.setState({
      regexString: '',
      testString: '',
      ast: null,
      flowNodes: [],
      flowEdges: [],
      aiExplanations: new LRUCache<string, string>({ maxSize: 100, ttl: 3600000 }),
      hoveredElementId: null,
      error: null,
      isLoading: false,
      lastValidState: null,
      requestVersion: 0,
      pendingExplanations: new Set<string>(),
      pendingNodeIds: new Map<string, Set<string>>(),
      aiErrorNodeIds: new Set<string>(),
    });

    // Reset fetch mock
    mockFetch.mockReset();
  });

  describe('Concurrent Requests', () => {
    it('should handle concurrent regex updates gracefully', async () => {
      const store = useRegexStore.getState();
      const startTime = Date.now();

      // 模拟多个并发的正则表达式更新
      const regexPatterns = [
        '\\d+',
        '[a-z]+',
        '\\w*',
        '[0-9]{3}',
        '(abc|def)'
      ];

      // 并发执行多个更新
      const promises = regexPatterns.map((pattern, index) => {
        return new Promise<void>((resolve) => {
          store.setRegexString(pattern);
          // 模拟异步操作完成 - 使用确定性延迟避免 Math.random()
          setTimeout(resolve, (index + 1) * 20);
        });
      });

      await Promise.all(promises);

      const duration = Date.now() - startTime;

      // 验证最终状态是一致的
      const finalState = useRegexStore.getState();
      expect(finalState.regexString).toBeDefined();
      // 由于并发操作，版本号应该被更新
      expect(finalState.requestVersion).toBeGreaterThanOrEqual(0);

      // 性能检查：并发处理应该在合理时间内完成
      // 使用动态阈值，考虑 CI 环境的性能差异
      const maxExpectedDuration = Math.max(3000, TEST_CONFIG.TIMEOUT_MS * 0.3);
      expect(duration).toBeLessThan(maxExpectedDuration);

      // 记录性能数据，便于分析
      console.log(`Concurrent updates completed in ${duration}ms (threshold: ${maxExpectedDuration}ms)`);
    }, Math.max(TEST_CONFIG.TIMEOUT_MS * 3, 30000)); // CI 友好：至少 30 秒或 3 倍配置时间

    it('should handle concurrent AI explanation requests', async () => {
      // Mock successful API responses
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({ explanation: 'Mock explanation' })
      });

      const store = useRegexStore.getState();
      
      // 模拟多个并发的 AI 解释请求
      const nodeIds = ['node-1', 'node-2', 'node-3', 'node-4', 'node-5'];
      const snippets = ['\\d', '[a-z]', '\\w', '[0-9]', '(abc)'];

      // 并发执行多个 AI 解释请求
      const promises = nodeIds.map((nodeId, index) => 
        store._fetchExplanationForNode(nodeId, snippets[index])
      );

      await Promise.all(promises);

      // 验证所有解释都被正确存储
      const finalState = useRegexStore.getState();
      expect(finalState.aiExplanations.getSize()).toBe(5);
      
      nodeIds.forEach(nodeId => {
        expect(finalState.aiExplanations.has(nodeId)).toBe(true);
      });
    }, Math.max(TEST_CONFIG.TIMEOUT_MS * 3, 30000)); // CI 友好：至少 30 秒或 3 倍配置时间

    it('should handle rapid successive updates without memory leaks', async () => {
      const store = useRegexStore.getState();
      const initialMemory = process.memoryUsage?.()?.heapUsed || 0;
      
      // 快速连续更新正则表达式
      for (let i = 0; i < 100; i++) {
        store.setRegexString(`test-pattern-${i}`);
        // 短暂延迟模拟用户输入
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      // 等待所有防抖操作完成
      await new Promise(resolve => setTimeout(resolve, 500));

      const finalMemory = process.memoryUsage?.()?.heapUsed || 0;
      const memoryIncrease = finalMemory - initialMemory;
      
      // 验证内存增长在合理范围内（小于 10MB）
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
      
      // 验证最终状态
      const finalState = useRegexStore.getState();
      expect(finalState.regexString).toMatch(/test-pattern-\d+/);
    }, Math.max(TEST_CONFIG.TIMEOUT_MS * 4, 40000)); // CI 友好：至少 40 秒或 4 倍配置时间
  });

  describe('Performance Limits', () => {
    it('should handle large number of nodes gracefully', async () => {
      const store = useRegexStore.getState();
      
      // 模拟一个会产生大量节点的复杂正则表达式
      const complexRegex = 'a'.repeat(50) + '+' + 'b'.repeat(50) + '*';
      
      const startTime = Date.now();
      store.setRegexString(complexRegex);
      
      // 等待处理完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      // 验证处理时间在合理范围内（小于 5 秒）
      expect(processingTime).toBeLessThan(5000);
      
      const finalState = useRegexStore.getState();
      // 应该有错误或者成功处理
      expect(finalState.error !== null || finalState.flowNodes.length > 0).toBe(true);
    }, Math.max(TEST_CONFIG.TIMEOUT_MS * 2.5, 25000)); // CI 友好：至少 25 秒或 2.5 倍配置时间

    it('should handle version conflicts correctly', async () => {
      const store = useRegexStore.getState();
      
      // 快速连续设置不同的正则表达式
      store.setRegexString('pattern-1');
      store.setRegexString('pattern-2');
      store.setRegexString('pattern-3');
      
      // 等待所有操作完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const finalState = useRegexStore.getState();
      
      // 验证最终状态是最后一个设置的模式
      expect(finalState.regexString).toBe('pattern-3');
      expect(finalState.requestVersion).toBeGreaterThan(0);
    });
  });

  describe('Error Recovery Under Load', () => {
    it('should recover from errors during concurrent operations', async () => {
      const store = useRegexStore.getState();
      
      // 设置一个有效的正则表达式作为基准
      store.setRegexString('valid-pattern');
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 然后设置多个无效的正则表达式
      const invalidPatterns = ['[', '(', '*', '+', '?'];
      
      for (const pattern of invalidPatterns) {
        store.setRegexString(pattern);
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // 等待所有操作完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const finalState = useRegexStore.getState();
      
      // 验证错误恢复机制工作正常
      expect(finalState.error).toBeDefined();
      expect(finalState.regexString).toBe('?'); // 最后设置的模式
    });
  });

  describe('Memory Management', () => {
    it('should limit AI explanation cache size', async () => {
      // Mock successful API responses
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({ explanation: 'Mock explanation' })
      });

      const store = useRegexStore.getState();
      
      // 添加大量的 AI 解释到缓存
      for (let i = 0; i < 1000; i++) {
        await store._fetchExplanationForNode(`node-${i}`, `pattern-${i}`);
      }
      
      const finalState = useRegexStore.getState();
      
      // 验证缓存大小在合理范围内
      // 注意：这里我们假设有某种缓存限制机制
      expect(finalState.aiExplanations.getSize()).toBeLessThanOrEqual(1000);
    }, Math.max(TEST_CONFIG.TIMEOUT_MS * 5, 50000)); // CI 友好：至少 50 秒或 5 倍配置时间，内存测试需要更长时间
  });

  // 恢复原始fetch，防止影响其他测试套件
  afterAll(() => {
    global.fetch = originalFetch;
  });
});
