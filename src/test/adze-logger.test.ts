/**
 * Adze Logger 测试文件
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { adzeLog, adzeStore } from '../lib/adze-logger';

// Mock console methods
const mockConsole = {
  log: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  debug: vi.fn(),
};

// 保存原始的 console 方法
const originalConsole = { ...console };

describe('Adze Logger', () => {
  beforeEach(() => {
    // 重置所有 mock
    vi.clearAllMocks();
    
    // Mock console methods
    Object.assign(console, mockConsole);
    
    // 清除日志缓存
    adzeStore.clearCache();
  });

  afterEach(() => {
    // 恢复原始的 console 方法
    Object.assign(console, originalConsole);
  });

  describe('基础日志方法', () => {
    it('应该能够记录 debug 日志', () => {
      adzeLog.debug('测试 debug 日志', { test: true });
      
      // 在测试环境中，日志可能被静默，所以我们检查缓存
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录 info 日志', () => {
      adzeLog.info('测试 info 日志', { component: 'test' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录 warn 日志', () => {
      adzeLog.warn('测试 warn 日志', { warning: 'test' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录 error 日志', () => {
      const testError = new Error('测试错误');
      adzeLog.error('测试 error 日志', testError, { context: 'test' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录 success 日志', () => {
      adzeLog.success('测试 success 日志', { result: 'ok' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录 fail 日志', () => {
      adzeLog.fail('测试 fail 日志', { reason: 'test' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });
  });

  describe('API 日志方法', () => {
    it('应该能够记录 API 请求', () => {
      adzeLog.api.request('POST', '/api/test', { data: 'test' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录成功的 API 响应', () => {
      adzeLog.api.response('GET', '/api/test', 200, 100, { result: 'success' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录失败的 API 响应', () => {
      adzeLog.api.response('POST', '/api/test', 400, 50, { error: 'bad request' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录 API 错误', () => {
      const apiError = new Error('API 连接失败');
      adzeLog.api.error('GET', '/api/test', apiError, { timeout: 5000 });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });
  });

  describe('性能日志方法', () => {
    it('应该能够使用计时器模式记录性能', (done) => {
      const timer = adzeLog.performance.start('测试操作');
      
      setTimeout(() => {
        const duration = timer.end({ testData: 'performance' });
        
        expect(typeof duration).toBe('number');
        expect(duration).toBeGreaterThan(0);
        
        const cache = adzeStore.cache;
        expect(cache.length).toBeGreaterThan(0);
        
        done();
      }, 10);
    });

    it('应该能够直接记录性能数据', () => {
      adzeLog.performance.measure('测试操作', 123.45, { operation: 'test' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });
  });

  describe('UI 日志方法', () => {
    it('应该能够记录点击事件', () => {
      adzeLog.ui.click('test-button', { position: { x: 100, y: 200 } });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录输入事件', () => {
      adzeLog.ui.input('test-input', 'test value', { length: 10 });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录导航事件', () => {
      adzeLog.ui.navigation('home', 'settings', { userId: 123 });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录 UI 错误', () => {
      const uiError = new Error('组件渲染失败');
      adzeLog.ui.error('TestComponent', uiError, { props: { test: true } });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });
  });

  describe('正则表达式日志方法', () => {
    it('应该能够记录成功的正则解析', () => {
      adzeLog.regex.parse('/test/g', true, { flags: ['g'] });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录失败的正则解析', () => {
      adzeLog.regex.parse('/[unclosed', false, { error: 'Unclosed bracket' });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录正则匹配结果', () => {
      adzeLog.regex.match('/test/g', 'test string test', 2, { 
        matches: ['test', 'test'] 
      });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够记录正则错误', () => {
      const regexError = new Error('Invalid regex');
      adzeLog.regex.error('/(?<invalid/g', regexError, { position: 9 });
      
      const cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
    });
  });

  describe('日志存储和缓存', () => {
    it('应该能够访问日志缓存', () => {
      adzeLog.info('测试缓存');
      
      const cache = adzeStore.cache;
      expect(Array.isArray(cache)).toBe(true);
      expect(cache.length).toBeGreaterThan(0);
    });

    it('应该能够清除日志缓存', () => {
      adzeLog.info('测试缓存清除');
      
      let cache = adzeStore.cache;
      expect(cache.length).toBeGreaterThan(0);
      
      adzeStore.clearCache();
      
      cache = adzeStore.cache;
      expect(cache.length).toBe(0);
    });

    it('应该能够访问全局配置', () => {
      const config = adzeStore.configuration;
      
      expect(config).toBeDefined();
      expect(config.cache).toBe(true);
      expect(config.cacheSize).toBe(500);
    });
  });
});
