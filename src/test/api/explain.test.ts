import { describe, it, expect } from 'vitest';
import { POST } from '../../app/api/explain/route';

describe('/api/explain', () => {
  describe('POST', () => {
    it('should return explanation for valid snippet', async () => {
      const request = new Request('http://localhost:3000/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          snippet: '\\d+',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('explanation');
      expect(data.explanation).toContain('\\d+');
      expect(data.explanation).toMatch(/模拟解释|mock|explanation/i);
    });

    it('should return 400 for missing snippet', async () => {
      const request = new Request('http://localhost:3000/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
      expect(data.error).toBe('Missing or invalid snippet field');
    });

    it('should return 400 for invalid snippet type', async () => {
      const request = new Request('http://localhost:3000/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          snippet: 123,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
      expect(data.error).toBe('Missing or invalid snippet field');
    });

    it('should return 400 for empty snippet', async () => {
      const request = new Request('http://localhost:3000/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          snippet: '   ',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
      expect(data.error).toBe('Missing or invalid snippet field');
    });

    it('should return 400 for invalid JSON', async () => {
      const request = new Request('http://localhost:3000/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: 'invalid json',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data).toHaveProperty('error');
      expect(data.error).toBe('Invalid JSON in request body');
    });

    it('should handle complex regex snippets', async () => {
      const complexSnippet = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';
      
      const request = new Request('http://localhost:3000/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          snippet: complexSnippet,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('explanation');
      expect(data.explanation).toContain(complexSnippet);
    });

    it('should handle special characters in snippet', async () => {
      const specialSnippet = '[\\s\\S]*?';
      
      const request = new Request('http://localhost:3000/api/explain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          snippet: specialSnippet,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveProperty('explanation');
      expect(data.explanation).toContain(specialSnippet);
    });
  });
});
