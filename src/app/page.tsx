"use client"

import MainLayout from "@components/layout/MainLayout"
import InteractiveRegexInput from "@panels/InteractiveRegexInput"
import TestStringInputPanel from "@panels/TestStringInputPanel"
import ExplanationPanel from "@panels/ExplanationPanel"
import GraphPanel from "@panels/GraphPanel"
import { useOptimizedRegexStore } from '@/hooks/useRegexStore'
import { useMemo } from 'react'





/**
 * 主页组件 - 正则表达式可视化工具
 *
 * 功能：
 * - 提供正则表达式输入和测试字符串输入
 * - 展示可视化图形面板
 * - 开发环境下显示调试面板
 *
 * 布局：
 * - 左侧：输入面板 + 调试面板（开发环境）
 * - 右侧：图形可视化面板
 */
export default function Home() {
  const {
    regexString,
    testString,
    setTestString,
    matches,
    nodes,
    edges
  } = useOptimizedRegexStore()

  // 判断是否有真正的可视化数据（排除占位符）
  const hasVisualizationData = nodes && nodes.length > 0 &&
    !nodes.every(node => node.id === 'empty-placeholder' || node.id === 'error-placeholder')

  // 用 useMemo 包装 leftPanel，提升微小性能
  const leftPanel = useMemo(() => (
    <div className="h-full flex flex-col space-y-4">
      <div className="h-[30%] min-h-[120px]">
        <InteractiveRegexInput />
      </div>
      <div className="h-[25%] min-h-[100px]">
        <TestStringInputPanel
          value={testString}
          onChange={setTestString}
          matches={matches}
          regexString={regexString}
          aria-label="测试字符串"
        />
      </div>
      <div className="h-[30%] min-h-[120px]">
        <ExplanationPanel />
      </div>

    </div>
  ), [regexString, testString, matches]) // 移除Action依赖，它们是稳定引用

  // 用 useMemo 包装 rightPanel，提升微小性能
  const rightPanel = useMemo(() => (
    <GraphPanel
      nodes={nodes}
      edges={edges}
    />
  ), [nodes, edges])

  return (
    <MainLayout
      leftPanel={leftPanel}
      rightPanel={rightPanel}
    />
  )
}
