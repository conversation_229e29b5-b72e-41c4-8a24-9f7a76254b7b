import { NextResponse } from 'next/server';
import { logger, ERR, INFO, SUCCESS } from '../../../lib/logger';
import { API_CONFIG } from '../../../lib/config';
import { createHash } from 'crypto';

// 防止内存泄漏的清理间隔管理
let cleanupInterval: NodeJS.Timeout | null = null;

/**
 * API 请求接口定义
 */
export interface ExplainRequest {
  snippet: string;
}

/**
 * API 响应接口定义
 */
export interface ExplainResponse {
  explanation: string;
}

/**
 * API 错误响应接口定义
 */
export interface ExplainErrorResponse {
  error: string;
  code?: string;
}

/**
 * 简单的内存速率限制器
 * 生产环境应使用 Redis 或其他持久化存储
 */
class RateLimiter {
  private requests = new Map<string, number[]>();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests?: number, windowMs: number = 60000) {
    // 支持环境变量控制速率限制
    this.maxRequests = maxRequests || parseInt(process.env.RATE_LIMIT_PER_MINUTE || '10');
    this.windowMs = windowMs;
  }

  isAllowed(clientId: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(clientId) || [];

    // 清理过期的请求记录
    const validRequests = requests.filter(time => now - time < this.windowMs);

    if (validRequests.length >= this.maxRequests) {
      return false;
    }

    validRequests.push(now);
    this.requests.set(clientId, validRequests);

    return true;
  }

  cleanup(): void {
    const now = Date.now();
    for (const [clientId, requests] of this.requests.entries()) {
      const validRequests = requests.filter(time => now - time < this.windowMs);
      if (validRequests.length === 0) {
        this.requests.delete(clientId);
      } else {
        this.requests.set(clientId, validRequests);
      }
    }
  }
}

const rateLimiter = new RateLimiter();

// 定期清理过期的速率限制记录，防止内存泄漏
if (!cleanupInterval) {
  cleanupInterval = setInterval(() => rateLimiter.cleanup(), 60000);
}

/**
 * 改善的 CORS 检查函数
 */
function isAllowedOrigin(origin: string): boolean {
  if (process.env.NODE_ENV === 'development') {
    return origin?.startsWith('http://localhost:') ||
           origin?.startsWith('http://127.0.0.1:');
  }
  return API_CONFIG.CORS_ORIGINS.includes(origin);
}

/**
 * 验证请求体是否符合 ExplainRequest 接口
 * 使用严格的类型检查，避免 any 类型
 */
function validateExplainRequest(body: unknown): body is ExplainRequest {
  return (
    body !== null &&
    body !== undefined &&
    typeof body === 'object' &&
    'snippet' in body &&
    typeof (body as Record<string, unknown>).snippet === 'string' &&
    ((body as Record<string, unknown>).snippet as string).trim().length > 0
  );
}

/**
 * 生成内容的安全哈希值用于日志记录
 */
function generateContentHash(content: string): string {
  return createHash('sha256').update(content).digest('hex').substring(0, 8);
}

/**
 * 清理函数，用于防止内存泄漏（导出供测试使用）
 */
export function cleanup() {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
  }
}

/**
 * OPTIONS /api/explain
 *
 * 处理 CORS 预检请求
 *
 * @param request - Next.js Request 对象
 * @returns Promise<NextResponse>
 */
export async function OPTIONS(request: Request): Promise<NextResponse> {
  const origin = request.headers.get('origin');

  if (origin && isAllowedOrigin(origin)) {
    return new NextResponse(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400', // 24 hours
      },
    });
  }

  return new NextResponse(null, { status: 403 });
}

/**
 * POST /api/explain
 *
 * 接收正则表达式片段并返回AI解释
 *
 * @param request - Next.js Request 对象
 * @returns Promise<NextResponse<ExplainResponse | ExplainErrorResponse>>
 */
export async function POST(request: Request): Promise<NextResponse<ExplainResponse | ExplainErrorResponse>> {
  const startTime = Date.now();
  const method = 'POST';
  const path = '/api/explain';

  // 记录 API 请求
  INFO`处理 API 请求: ${method} ${path}`;

  // 1. CORS 检查（改善的实现）
  const origin = request.headers.get('origin');
  if (origin && !isAllowedOrigin(origin)) {
    const duration = Date.now() - startTime;
    logger.warn('CORS not allowed', { method, path, origin, duration });

    return NextResponse.json<ExplainErrorResponse>(
      { error: 'Origin not allowed', code: 'CORS_ERROR' },
      { status: 403 }
    );
  }

  // 2. 速率限制检查
  const clientId = request.headers.get('x-forwarded-for') ||
                   request.headers.get('x-real-ip') ||
                   'unknown';

  if (!rateLimiter.isAllowed(clientId)) {
    const duration = Date.now() - startTime;
    logger.warn('Rate limit exceeded', { method, path, clientId, duration });

    return NextResponse.json<ExplainErrorResponse>(
      { error: 'Too many requests', code: 'RATE_LIMIT_EXCEEDED' },
      { status: 429 }
    );
  }

  // 3. API Key 验证（OpenRouter API Key 必需）
  if (!process.env.OPENROUTER_API_KEY) {
    const duration = Date.now() - startTime;
    ERR`OpenRouter API key not configured - ${method} ${path}`;
    logger.error('Service not configured', { method, path, duration });

    return NextResponse.json<ExplainErrorResponse>(
      { error: 'Service not configured', code: 'SERVICE_NOT_CONFIGURED' },
      { status: 503 } // Service Unavailable
    );
  }

  try {
    // 解析请求的 JSON body，使用严格类型
    let body: unknown;
    try {
      body = await request.json();
    } catch (parseError) {
      const duration = Date.now() - startTime;
      logger.warn('JSON parse error', { method, path, duration });

      return NextResponse.json<ExplainErrorResponse>(
        { error: 'Invalid JSON in request body', code: 'INVALID_JSON' },
        { status: 400 }
      );
    }

    // 严格类型校验
    if (!validateExplainRequest(body)) {
      const duration = Date.now() - startTime;
      logger.warn('Invalid request body', { method, path, duration });

      return NextResponse.json<ExplainErrorResponse>(
        { error: 'Missing or invalid snippet field', code: 'INVALID_SNIPPET' },
        { status: 400 }
      );
    }

    const { snippet } = body;

    // 添加请求大小限制
    if (JSON.stringify(body).length > 1000) {
      const duration = Date.now() - startTime;
      logger.warn('Request too large', { method, path, duration });

      return NextResponse.json<ExplainErrorResponse>(
        { error: 'Request too large', code: 'REQUEST_TOO_LARGE' },
        { status: 413 }
      );
    }

    // 输入清理和长度限制
    const cleanSnippet = snippet.trim().slice(0, 500);
    const contentHash = generateContentHash(cleanSnippet);



    // 获取 OpenRouter API Key（已在前面验证过存在）
    const openrouterApiKey = process.env.OPENROUTER_API_KEY!;

    // 构建 OpenRouter API 请求
    const systemPrompt = `你是一位精通所有正则表达式方言的专家。你的任务是为给定的正则表达式片段，提供一个简洁、清晰、且对初学者友好的解释。

这个片段是：\`${cleanSnippet}\`

请直接返回解释文本，不要添加任何"好的"、"当然"等多余的开场白或结束语。`;

    const openrouterRequestBody = {
      model: "anthropic/claude-3-haiku",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: cleanSnippet
        }
      ]
    };

    // 创建超时控制
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), API_CONFIG.TIMEOUT_MS);
    });

    try {
      // 调用 OpenRouter API
      const openrouterResponse = await Promise.race([
        fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${openrouterApiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
            'X-Title': 'RegexVision - Interactive Regex Visualizer'
          },
          body: JSON.stringify(openrouterRequestBody)
        }),
        timeoutPromise
      ]);

      if (!openrouterResponse.ok) {
        const errorText = await openrouterResponse.text();
        const duration = Date.now() - startTime;
        ERR`OpenRouter API error - HTTP ${openrouterResponse.status}: ${errorText}`;
        logger.error('OpenRouter API error', {
          method,
          path,
          status: openrouterResponse.status,
          contentHash,
          duration
        });

        return NextResponse.json<ExplainErrorResponse>(
          { error: 'AI service temporarily unavailable', code: 'UPSTREAM_ERROR' },
          { status: 502 }
        );
      }

      const openrouterData = await openrouterResponse.json();

      // 提取 AI 响应内容
      const explanation = openrouterData?.choices?.[0]?.message?.content;
      if (!explanation) {
        const duration = Date.now() - startTime;
        ERR`Invalid OpenRouter response format - missing content`;
        logger.error('Invalid OpenRouter response format', {
          method,
          path,
          response: JSON.stringify(openrouterData).substring(0, 200),
          duration
        });

        return NextResponse.json<ExplainErrorResponse>(
          { error: 'Invalid response from AI service', code: 'INVALID_RESPONSE' },
          { status: 502 }
        );
      }

      const duration = Date.now() - startTime;
      SUCCESS`API 请求成功处理 - ${method} ${path} (${duration}ms)`;

      return NextResponse.json<ExplainResponse>({
        explanation: explanation.trim()
      });

    } catch (error) {
      const duration = Date.now() - startTime;

      if (error instanceof Error && error.message === 'Request timeout') {
        logger.warn('OpenRouter API timeout', { method, path, duration, timeout: API_CONFIG.TIMEOUT_MS });

        return NextResponse.json<ExplainErrorResponse>(
          { error: 'Request timeout', code: 'TIMEOUT' },
          { status: 408 }
        );
      }

      // 网络错误或其他错误
      ERR`OpenRouter API request failed - ${error}`;
      logger.error('OpenRouter API request failed', {
        method,
        path,
        duration,
        contentHash,
        error: error instanceof Error ? error.message : String(error)
      });

      return NextResponse.json<ExplainErrorResponse>(
        { error: 'Failed to connect to AI service', code: 'NETWORK_ERROR' },
        { status: 502 }
      );
    }

  } catch (error) {
    const duration = Date.now() - startTime;

    // 使用统一日志系统记录错误
    ERR`Error in /api/explain - ${error}`;
    logger.error('Error in /api/explain', {
      method,
      path,
      duration,
      error: error instanceof Error ? error.message : String(error)
    });

    return NextResponse.json<ExplainErrorResponse>(
      { error: 'Internal server error', code: 'INTERNAL_ERROR' },
      { status: 500 }
    );
  }
}
