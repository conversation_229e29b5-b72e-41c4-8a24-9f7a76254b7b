import { NextResponse } from 'next/server';

/**
 * 健康检查响应接口
 */
export interface HealthResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    openrouter: 'configured' | 'not_configured';
    database: 'not_applicable';
  };
  environment: string;
}

/**
 * GET /api/health
 *
 * 健康检查端点，用于监控服务状态
 *
 * @returns Promise<NextResponse<HealthResponse>>
 */
export async function GET(): Promise<NextResponse<HealthResponse>> {
  const startTime = Date.now();

  try {
    // 检查关键服务状态
    const openrouterConfigured = !!process.env.OPENROUTER_API_KEY;
    
    // 计算运行时间（从进程启动开始）
    const uptime = process.uptime();
    
    // 获取版本信息
    const version = process.env.npm_package_version || '0.1.0';
    
    // 确定整体健康状态
    const isHealthy = openrouterConfigured;
    
    const healthResponse: HealthResponse = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(uptime),
      version,
      services: {
        openrouter: openrouterConfigured ? 'configured' : 'not_configured',
        database: 'not_applicable' // 当前不使用数据库
      },
      environment: process.env.NODE_ENV || 'unknown'
    };

    const statusCode = isHealthy ? 200 : 503;
    const duration = Date.now() - startTime;

    // 简单的日志记录（不使用复杂的日志系统避免循环依赖）
    // 健康检查日志已禁用

    return NextResponse.json(healthResponse, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`Health check failed in ${duration}ms:`, error);

    const errorResponse: HealthResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: 0,
      version: 'unknown',
      services: {
        openrouter: 'not_configured',
        database: 'not_applicable'
      },
      environment: process.env.NODE_ENV || 'unknown'
    };

    return NextResponse.json(errorResponse, { status: 503 });
  }
}

/**
 * HEAD /api/health
 *
 * 轻量级健康检查，只返回状态码
 *
 * @returns Promise<NextResponse>
 */
export async function HEAD(): Promise<NextResponse> {
  try {
    const isHealthy = !!process.env.OPENROUTER_API_KEY;
    const statusCode = isHealthy ? 200 : 503;
    
    return new NextResponse(null, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  } catch {
    return new NextResponse(null, { status: 503 });
  }
}
