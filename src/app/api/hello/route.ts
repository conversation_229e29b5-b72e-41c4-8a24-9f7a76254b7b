import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { logger, ERR, INFO, SUCCESS } from '@/lib/logger';

export interface HelloResponse {
  message: string;
  timestamp: string;
  method: string;
  userAgent?: string;
}

/**
 * GET /api/hello
 * 
 * 示例 API 路由，展示如何使用新的 Adze 日志系统
 */
export async function GET(request: NextRequest): Promise<NextResponse<HelloResponse>> {
  const startTime = Date.now();
  

  
  try {
    const userAgent = request.headers.get('user-agent');
    
    const response: HelloResponse = {
      message: 'Hello World from RegexAI!',
      timestamp: new Date().toISOString(),
      method: 'GET',
      userAgent: userAgent || undefined
    };
    
    const duration = Date.now() - startTime;
    

    
    return NextResponse.json(response, { status: 200 });
    
  } catch (err) {
    const duration = Date.now() - startTime;
    

    
    return NextResponse.json(
      { error: 'Internal Server Error' }, 
      { status: 500 }
    );
  }
}

/**
 * POST /api/hello
 * 
 * 展示如何处理 POST 请求并记录请求体
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  

  
  try {
    const body = await request.json();
    

    
    // 模拟一些处理逻辑
    if (!body.name) {

      return NextResponse.json(
        { error: 'Name is required' }, 
        { status: 400 }
      );
    }
    
    const response = {
      message: `Hello, ${body.name}!`,
      timestamp: new Date().toISOString(),
      receivedData: body
    };
    
    const duration = Date.now() - startTime;

    
    return NextResponse.json(response, { status: 200 });
    
  } catch (err) {
    const duration = Date.now() - startTime;

    
    return NextResponse.json(
      { error: 'Invalid JSON or server error' }, 
      { status: 500 }
    );
  }
}
