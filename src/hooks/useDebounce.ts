import { useCallback, useRef, useEffect } from 'react';
import debounce from 'lodash.debounce';
import { PerformanceTimer } from '../lib/performance';
import { IS_DEVELOPMENT } from '../lib/constants';

/**
 * 防抖 Hook，支持异步函数
 * @param callback 要防抖的回调函数
 * @param delay 防抖延迟时间（毫秒）
 * @param deps 依赖数组，当依赖变化时重新创建防抖函数
 * @returns 防抖后的函数
 */
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T {
  const callbackRef = useRef(callback);

  // 更新回调函数引用
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // 创建防抖函数
  const debouncedCallback = useCallback(
    debounce((...args: Parameters<T>) => {
      return callbackRef.current(...args);
    }, delay),
    [delay, ...deps]
  );

  // 清理函数
  useEffect(() => {
    return () => {
      debouncedCallback.cancel();
    };
  }, [debouncedCallback]);

  return debouncedCallback as unknown as T;
}

/**
 * 异步防抖 Hook，专门用于异步函数
 * @param asyncCallback 异步回调函数
 * @param delay 防抖延迟时间（毫秒）
 * @param deps 依赖数组
 * @returns 防抖后的异步函数
 */
export function useAsyncDebounce<T extends (...args: any[]) => Promise<any>>(
  asyncCallback: T,
  delay: number,
  deps: React.DependencyList = []
): T {
  const callbackRef = useRef(asyncCallback);
  const pendingPromiseRef = useRef<Promise<any> | null>(null);

  // 更新回调函数引用
  useEffect(() => {
    callbackRef.current = asyncCallback;
  }, [asyncCallback]);

  // 创建防抖函数
  const debouncedCallback = useCallback(
    debounce(async (...args: Parameters<T>) => {
      try {
        // 如果有正在执行的 Promise，等待它完成
        if (pendingPromiseRef.current) {
          await pendingPromiseRef.current;
        }

        // 执行新的异步操作
        const promise = callbackRef.current(...args);
        pendingPromiseRef.current = promise;

        const result = await promise;
        pendingPromiseRef.current = null;

        return result;
      } catch (error) {
        pendingPromiseRef.current = null;
        throw error;
      }
    }, delay),
    [delay, ...deps]
  );

  // 清理函数
  useEffect(() => {
    return () => {
      debouncedCallback.cancel();
      pendingPromiseRef.current = null;
    };
  }, [debouncedCallback]);

  return debouncedCallback as unknown as T;
}

/**
 * 防抖函数类型定义
 * 为了避免返回值混淆，统一返回 Promise<void>
 */
type DebouncedFunc<T extends (...args: any[]) => any> =
  ((...args: Parameters<T>) => Promise<void>) & { cancel: () => void };

/**
 * 简单的防抖函数，用于非 React 环境
 *
 * 返回值语义说明：
 * - 统一返回 Promise<void>，无论原函数是同步还是异步
 * - 不返回原函数的实际返回值，因为调用是延迟的
 * - Promise 在延迟执行完成后 resolve，如果执行出错不会 reject
 * - 如果需要原函数的返回值，请在原函数内部处理结果
 *
 * 异步检测说明：
 * - 仅使用 func.constructor.name === 'AsyncFunction' 检测
 * - 避免调用原函数产生副作用
 *
 * @param func 要防抖的函数（同步或异步）
 * @param delay 防抖延迟时间（毫秒）
 * @returns 防抖后的函数，统一返回 Promise<void>
 *
 * @example
 * ```typescript
 * // 同步函数
 * const debouncedSync = createDebounce(() => console.log('sync'), 300);
 * await debouncedSync(); // 返回 Promise<void>
 *
 * // 异步函数
 * const debouncedAsync = createDebounce(async () => { await api.call(); }, 300);
 * await debouncedAsync(); // 返回 Promise<void>
 * ```
 */
export function createDebounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): DebouncedFunc<T> {
  let timeoutId: NodeJS.Timeout | null = null;

  const debouncedFunction = ((...args: Parameters<T>) => {
    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 检查原函数是否为异步函数（避免执行副作用）
    const isAsync = func.constructor.name === 'AsyncFunction';

    // 统一返回 Promise<void>，避免类型混淆
    return new Promise<void>((resolve) => {
      timeoutId = setTimeout(async () => {
        try {
          if (isAsync) {
            await func(...args);
          } else {
            func(...args);
          }
        } catch (error) {
          console.error('Debounced function error:', error);
        }
        resolve();
      }, delay);
    });
  }) as DebouncedFunc<T>;

  // 添加 cancel 方法
  debouncedFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  return debouncedFunction;
}

/**
 * 带性能监控的防抖 Hook
 * @param callback 要防抖的回调函数
 * @param delay 防抖延迟时间（毫秒）
 * @param label 性能监控标签
 * @param deps 依赖数组
 * @returns 防抖后的函数
 */
export function usePerformanceDebounce<T extends (...args: any[]) => Promise<any>>(
  callback: T,
  delay: number,
  label: string = 'debounced-function',
  deps: React.DependencyList = []
): T {
  const callbackRef = useRef(callback);

  // 更新回调函数引用
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // 创建带性能监控的防抖函数
  const debouncedCallback = useCallback(
    debounce(async (...args: Parameters<T>) => {
      const timer = new PerformanceTimer(label);

      try {
        timer.mark('start');
        const result = await callbackRef.current(...args);
        timer.mark('end');

        const totalTime = timer.getTotalTime();

        // 记录性能指标（简化版）
        if (IS_DEVELOPMENT) {
          // 性能日志已禁用
        }

        return result;
      } catch (error) {
        timer.mark('error');
        const totalTime = timer.getTotalTime();

        if (IS_DEVELOPMENT) {
          console.warn(`⚠️ ${label} failed after ${totalTime}ms:`, error);
        }

        throw error;
      }
    }, delay),
    [delay, label, ...deps]
  );

  // 清理函数
  useEffect(() => {
    return () => {
      debouncedCallback.cancel();
    };
  }, [debouncedCallback]);

  return debouncedCallback as unknown as T;
}
