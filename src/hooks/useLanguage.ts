import { useState, useCallback, useEffect } from 'react';
import { setLocale, getLocale, type Locale } from '../lib/i18n';

/**
 * 语言管理 Hook
 */
export function useLanguage() {
  const [currentLocale, setCurrentLocale] = useState<Locale>(getLocale());

  // 切换语言
  const changeLanguage = useCallback((locale: Locale) => {
    setLocale(locale);
    setCurrentLocale(locale);
    
    // 保存到 localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('regex-ai-locale', locale);
    }
  }, []);

  // 切换到下一个语言
  const toggleLanguage = useCallback(() => {
    const nextLocale: Locale = currentLocale === 'zh-CN' ? 'en-US' : 'zh-CN';
    changeLanguage(nextLocale);
  }, [currentLocale, changeLanguage]);

  // 获取语言显示名称
  const getLanguageName = useCallback((locale: Locale) => {
    const names: Record<Locale, string> = {
      'zh-CN': '中文',
      'en-US': 'English',
    };
    return names[locale];
  }, []);

  // 获取所有可用语言
  const getAvailableLanguages = useCallback(() => {
    return [
      { code: 'zh-CN' as Locale, name: '中文', flag: '🇨🇳' },
      { code: 'en-US' as Locale, name: 'English', flag: '🇺🇸' },
    ];
  }, []);

  // 从 localStorage 恢复语言设置 - 使用 suppressHydrationWarning 避免 hydration 问题
  useEffect(() => {
    // 延迟到客户端挂载后再访问 localStorage，避免 hydration 不匹配
    const timer = setTimeout(() => {
      if (typeof window !== 'undefined') {
        const savedLocale = localStorage.getItem('regex-ai-locale') as Locale;
        if (savedLocale && (savedLocale === 'zh-CN' || savedLocale === 'en-US')) {
          changeLanguage(savedLocale);
        }
      }
    }, 0);

    return () => clearTimeout(timer);
  }, [changeLanguage]);

  return {
    currentLocale,
    changeLanguage,
    toggleLanguage,
    getLanguageName,
    getAvailableLanguages,
    isChineseMode: currentLocale === 'zh-CN',
    isEnglishMode: currentLocale === 'en-US',
  };
}

/**
 * 语言感知的格式化 Hook
 */
export function useLocaleFormat() {
  const { currentLocale } = useLanguage();

  // 格式化数字
  const formatNumber = useCallback((num: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(currentLocale, options).format(num);
  }, [currentLocale]);

  // 格式化时间
  const formatTime = useCallback((ms: number) => {
    if (ms < 1000) {
      return `${ms}ms`;
    } else if (ms < 60000) {
      return `${(ms / 1000).toFixed(1)}s`;
    } else {
      return `${(ms / 60000).toFixed(1)}min`;
    }
  }, []);

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number) => {
    const units = currentLocale === 'zh-CN' 
      ? ['字节', 'KB', 'MB', 'GB'] 
      : ['B', 'KB', 'MB', 'GB'];
    
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(unitIndex === 0 ? 0 : 2)}${units[unitIndex]}`;
  }, [currentLocale]);

  // 格式化日期
  const formatDate = useCallback((date: Date, options?: Intl.DateTimeFormatOptions) => {
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    };
    
    return new Intl.DateTimeFormat(currentLocale, { ...defaultOptions, ...options }).format(date);
  }, [currentLocale]);

  // 格式化相对时间
  const formatRelativeTime = useCallback((timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (currentLocale === 'zh-CN') {
      if (diff < 60000) return '刚刚';
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
      return `${Math.floor(diff / 86400000)}天前`;
    } else {
      if (diff < 60000) return 'just now';
      if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
      return `${Math.floor(diff / 86400000)}d ago`;
    }
  }, [currentLocale]);

  return {
    formatNumber,
    formatTime,
    formatFileSize,
    formatDate,
    formatRelativeTime,
    currentLocale,
  };
}
