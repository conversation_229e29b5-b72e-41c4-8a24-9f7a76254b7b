import { useState, useEffect, useCallback } from 'react';
import { performanceMonitor, type PerformanceMetrics } from '../lib/performance';
import { useTranslation } from '../lib/i18n';

/**
 * 性能监控 Hook
 */
export function usePerformanceMonitor() {
  const { t } = useTranslation();
  const [stats, setStats] = useState(() => performanceMonitor.getStats());
  const [isVisible, setIsVisible] = useState(false);

  // 更新统计信息
  const updateStats = useCallback(() => {
    setStats(performanceMonitor.getStats());
  }, []);

  // 清除历史记录
  const clearHistory = useCallback(() => {
    performanceMonitor.clearHistory();
    updateStats();
  }, [updateStats]);

  // 切换显示状态
  const toggleVisibility = useCallback(() => {
    setIsVisible(prev => !prev);
  }, []);

  // 格式化性能数据
  const formatMetrics = useCallback((metrics: PerformanceMetrics | Partial<PerformanceMetrics>) => {
    return {
      parseTime: `${metrics.parseTime || 0}ms`,
      layoutTime: `${metrics.layoutTime || 0}ms`,
      totalTime: `${metrics.totalTime || 0}ms`,
      nodeCount: metrics.nodeCount || 0,
      edgeCount: metrics.edgeCount || 0,
      complexity: metrics.complexity || 0,
      regexLength: metrics.regexLength || 0,
      memoryUsage: metrics.memoryUsage 
        ? `${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB` 
        : 'N/A',
    };
  }, []);

  // 获取性能等级
  const getPerformanceLevel = useCallback((metrics: PerformanceMetrics | Partial<PerformanceMetrics>) => {
    const totalTime = metrics.totalTime || 0;
    const complexity = metrics.complexity || 0;

    if (totalTime > 2000 || complexity > 300) {
      return { level: 'poor', color: '#ef4444', label: '性能较差' };
    } else if (totalTime > 1000 || complexity > 150) {
      return { level: 'fair', color: '#f59e0b', label: '性能一般' };
    } else if (totalTime > 500 || complexity > 50) {
      return { level: 'good', color: '#10b981', label: '性能良好' };
    } else {
      return { level: 'excellent', color: '#06b6d4', label: '性能优秀' };
    }
  }, []);

  // 监听性能数据变化（在开发环境中）
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(updateStats, 1000);
      return () => clearInterval(interval);
    }
  }, [updateStats]);

  return {
    stats,
    isVisible,
    formatMetrics,
    getPerformanceLevel,
    updateStats,
    clearHistory,
    toggleVisibility,
    t,
  };
}

/**
 * 性能警告 Hook
 */
export function usePerformanceWarnings() {
  const [warnings, setWarnings] = useState<string[]>([]);
  const { t } = useTranslation();

  // 检查性能警告
  const checkWarnings = useCallback(() => {
    const stats = performanceMonitor.getStats();
    setWarnings(stats.warnings);
  }, []);

  // 清除警告
  const clearWarnings = useCallback(() => {
    setWarnings([]);
  }, []);

  // 监听性能数据变化
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(checkWarnings, 2000);
      return () => clearInterval(interval);
    }
  }, [checkWarnings]);

  return {
    warnings,
    hasWarnings: warnings.length > 0,
    clearWarnings,
    checkWarnings,
    t,
  };
}

/**
 * 实时性能指标 Hook
 */
export function useRealTimeMetrics() {
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics | null>(null);
  const [isRecording, setIsRecording] = useState(false);

  // 开始记录
  const startRecording = useCallback(() => {
    setIsRecording(true);
    setCurrentMetrics(null);
  }, []);

  // 停止记录
  const stopRecording = useCallback(() => {
    setIsRecording(false);
    const stats = performanceMonitor.getStats();
    if (stats.latest) {
      setCurrentMetrics(stats.latest);
    }
  }, []);

  // 重置指标
  const resetMetrics = useCallback(() => {
    setCurrentMetrics(null);
    setIsRecording(false);
  }, []);

  return {
    currentMetrics,
    isRecording,
    startRecording,
    stopRecording,
    resetMetrics,
  };
}

/**
 * 性能比较 Hook
 */
export function usePerformanceComparison() {
  const [baseline, setBaseline] = useState<PerformanceMetrics | null>(null);
  const [comparison, setComparison] = useState<{
    parseTimeDiff: number;
    layoutTimeDiff: number;
    totalTimeDiff: number;
    complexityDiff: number;
  } | null>(null);

  // 设置基准
  const setBaselineFromCurrent = useCallback(() => {
    const stats = performanceMonitor.getStats();
    if (stats.latest) {
      setBaseline(stats.latest);
    }
  }, []);

  // 比较当前性能与基准
  const compareWithBaseline = useCallback(() => {
    const stats = performanceMonitor.getStats();
    if (baseline && stats.latest) {
      const current = stats.latest;
      setComparison({
        parseTimeDiff: current.parseTime - baseline.parseTime,
        layoutTimeDiff: current.layoutTime - baseline.layoutTime,
        totalTimeDiff: current.totalTime - baseline.totalTime,
        complexityDiff: current.complexity - baseline.complexity,
      });
    }
  }, [baseline]);

  // 清除基准
  const clearBaseline = useCallback(() => {
    setBaseline(null);
    setComparison(null);
  }, []);

  return {
    baseline,
    comparison,
    hasBaseline: !!baseline,
    setBaselineFromCurrent,
    compareWithBaseline,
    clearBaseline,
  };
}
