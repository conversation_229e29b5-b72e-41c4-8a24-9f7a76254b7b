import { useMemo, useCallback, useEffect } from 'react';
import { useRegexStore } from '../store/regex.store';
import { performanceMonitor } from '../lib/performance';
import { useTranslation } from '../lib/i18n';
import type { Node, Edge } from '@xyflow/react';

/**
 * 优化的 Regex Store Hook，提供缓存和性能优化
 */
export function useOptimizedRegexStore() {
  // 基础状态
  const regexString = useRegexStore(state => state.regexString);
  const testString = useRegexStore(state => state.testString);
  const ast = useRegexStore(state => state.ast);
  const error = useRegexStore(state => state.error);
  const isLoading = useRegexStore(state => state.isLoading);
  const hoveredElementId = useRegexStore(state => state.hoveredElementId);

  // 匹配相关状态
  const matches = useRegexStore(state => state.matches);
  const matchResponse = useRegexStore(state => state.matchResponse);
  
  // 流程图数据 - 正确订阅变化
  const flowNodes = useRegexStore(state => state.flowNodes);
  const flowEdges = useRegexStore(state => state.flowEdges);
  const flowData = useMemo(() => ({
    nodes: flowNodes,
    edges: flowEdges
  }), [flowNodes, flowEdges]);

  // Actions - 通过 selector 订阅，避免 HMR 问题
  const setRegexString = useRegexStore(state => state.setRegexString);
  const setTestString = useRegexStore(state => state.setTestString);
  const setHoveredElementId = useRegexStore(state => state.setHoveredElementId);
  const clearError = useRegexStore(state => state.clearError);
  const recoverFromError = useRegexStore(state => state.recoverFromError);
  const _generateAstAndFlow = useRegexStore(state => state._generateAstAndFlow);
  const _fetchExplanationForNode = useRegexStore(state => state._fetchExplanationForNode);

  const actions = useMemo(() => ({
    setRegexString,
    setTestString,
    setHoveredElementId,
    clearError,
    recoverFromError,
    _generateAstAndFlow,
    _fetchExplanationForNode,
  }), [
    setRegexString,
    setTestString,
    setHoveredElementId,
    clearError,
    recoverFromError,
    _generateAstAndFlow,
    _fetchExplanationForNode,
  ]);

  // 计算派生状态
  const derivedState = useMemo(() => ({
    hasValidRegex: !!ast && !error,
    hasError: !!error,
    isEmpty: !regexString.trim(),
    nodeCount: flowData.nodes.length,
    edgeCount: flowData.edges.length,
    isRecoverable: error?.recoverable ?? false,
  }), [ast, error, regexString, flowData.nodes.length, flowData.edges.length]);

  // 初始化逻辑 - 处理默认正则表达式
  const _initialize = useRegexStore(state => state._initialize);
  useEffect(() => {
    _initialize();
  }, [_initialize]);

  return {
    // 基础状态
    regexString,
    testString,
    ast,
    error,
    isLoading,
    hoveredElementId,

    // 匹配相关状态
    matches,
    matchResponse,

    // 流程图数据
    ...flowData,

    // 派生状态
    ...derivedState,

    // Actions
    ...actions,
  };
}

/**
 * 只获取流程图数据的轻量级 Hook
 */
export function useFlowData(): { nodes: Node[]; edges: Edge[] } {
  return useRegexStore(
    useCallback(
      (state) => ({
        nodes: state.flowNodes,
        edges: state.flowEdges,
      }),
      []
    )
  );
}

/**
 * 只获取错误状态的 Hook
 */
export function useErrorState() {
  return useRegexStore(
    useCallback(
      (state) => ({
        error: state.error,
        isLoading: state.isLoading,
        hasError: !!state.error,
        isRecoverable: state.error?.recoverable ?? false,
        clearError: state.clearError,
        recoverFromError: state.recoverFromError,
      }),
      []
    )
  );
}

/**
 * 只获取输入状态的 Hook
 */
export function useInputState() {
  return useRegexStore(
    useCallback(
      (state) => ({
        regexString: state.regexString,
        testString: state.testString,
        setRegexString: state.setRegexString,
        setTestString: state.setTestString,
        isEmpty: !state.regexString.trim(),
      }),
      []
    )
  );
}

/**
 * 获取 AI 解释相关状态的 Hook
 */
export function useAIExplanations() {
  return useRegexStore(
    useCallback(
      (state) => ({
        aiExplanations: state.aiExplanations,
        hoveredElementId: state.hoveredElementId,
        setHoveredElementId: state.setHoveredElementId,
        fetchExplanation: state._fetchExplanationForNode,
      }),
      []
    )
  );
}

/**
 * 性能监控 Hook
 */
export function usePerformanceMetrics() {
  const { t } = useTranslation();

  return useRegexStore(
    useCallback(
      (state) => {
        const nodeCount = state.flowNodes.length;
        const edgeCount = state.flowEdges.length;
        const complexity = nodeCount + edgeCount * 0.5; // 简单的复杂度计算

        // 获取最新的性能统计
        const perfStats = performanceMonitor.getStats();

        // 获取解释缓存统计，观察真实效果
        const cacheStats = state.aiExplanations.getStats();

        return {
          nodeCount,
          edgeCount,
          complexity,
          isComplex: complexity > 50,
          hasData: nodeCount > 0,
          lastUpdate: state.lastValidState?.timestamp || Date.now(),
          // 性能统计
          performanceStats: perfStats,
          latestMetrics: perfStats.latest,
          averageMetrics: perfStats.average,
          performanceWarnings: perfStats.warnings,
          hasPerformanceIssues: perfStats.warnings.length > 0,
          // 解释缓存统计
          cacheStats,
          cacheHitRate: cacheStats.hitRate as number | undefined,
          cacheSize: cacheStats.size,
          // 本地化的性能描述
          getPerformanceDescription: () => {
            if (!perfStats.latest) return t('performance.totalTime');

            const totalTime = perfStats.latest.totalTime;
            if (totalTime > 2000) return t('performance.totalTime') + ' - ' + (t('performance.totalTime') === 'Total Time' ? 'Slow' : '慢');
            if (totalTime > 1000) return t('performance.totalTime') + ' - ' + (t('performance.totalTime') === 'Total Time' ? 'Fair' : '一般');
            if (totalTime > 500) return t('performance.totalTime') + ' - ' + (t('performance.totalTime') === 'Total Time' ? 'Good' : '良好');
            return t('performance.totalTime') + ' - ' + (t('performance.totalTime') === 'Total Time' ? 'Excellent' : '优秀');
          },
        };
      },
      [t]
    )
  );
}
