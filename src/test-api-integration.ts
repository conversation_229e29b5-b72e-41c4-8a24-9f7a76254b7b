/**
 * 测试 API 集成的简单脚本
 * 用于验证任务 5.3 的实现
 */

import { useRegexStore } from './store/regex.store';

// 模拟测试函数
async function testApiIntegration() {
  console.log('🧪 开始测试 API 集成...');
  
  // 获取 store 实例
  const store = useRegexStore.getState();
  
  // 测试数据
  const testNodeId = 'test-node-1';
  const testNodeContent = '\\d+';
  
  try {
    console.log(`📤 发送请求: nodeId=${testNodeId}, nodeContent=${testNodeContent}`);
    
    // 调用我们实现的函数
    await store._fetchExplanationForNode(testNodeId, testNodeContent);
    
    // 检查结果
    const explanation = store.aiExplanations.get(testNodeId);
    
    if (explanation) {
      console.log('✅ API 调用成功!');
      console.log(`📝 获取到的解释: ${explanation}`);
    } else {
      console.log('❌ 未获取到解释');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 导出测试函数
export { testApiIntegration };

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testApiIntegration();
}
