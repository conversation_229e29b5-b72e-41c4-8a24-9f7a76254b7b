import { create } from 'zustand';
import { Node, Edge } from '@xyflow/react';
import { parseRegex } from '../lib/parser';
import { getLayoutedElements } from '../lib/layout-client';
import { createDebounce } from '../hooks/useDebounce';
import { LRUCache } from '../lib/lru-cache';
import { DEBOUNCE_CONFIG, AI_CACHE_CONFIG } from '../lib/config';
// import { log } from '../lib/logger'; // 移除未使用的导入
import {
  ErrorRecoveryManager,
  ErrorHandler,
  ErrorType,
  createEmptyStatePlaceholder,
  createErrorStatePlaceholder,
  type EnhancedError,
  type StoreSnapshot
} from './error-recovery';
import {
  PerformanceTimer,
  performanceMonitor,
  calculateComplexity
} from '../lib/performance';
import { getMemoryUsage } from '../lib/utils/performance';
import { t } from '../lib/i18n';
import { logger } from '../lib/logger';
import type { AstRegExp } from 'regexp-tree/ast';
import { getMatches, type MatchResult, type MatchResponse } from '../lib/matcher';

// 定义 AST 节点的类型
type ASTNode = AstRegExp;

interface RegexState {
  regexString: string;
  testString: string;
  ast: ASTNode | null;
  flowNodes: Node[];
  flowEdges: Edge[];
  aiExplanations: LRUCache<string, string>; // 使用 LRU 缓存替代 Map
  hoveredElementId: string | null;
  error: EnhancedError | null; // 增强的错误信息
  isLoading: boolean; // 加载状态
  lastValidState: StoreSnapshot | null; // 最后有效状态
  requestVersion: number; // 请求版本号，用于解决并发竞争
  pendingExplanations: Set<string>; // 正在请求中的解释，用于并发去重（基于cacheKey）
  pendingNodeIds: Map<string, Set<string>>; // 正在请求中的节点ID映射，key为cacheKey，value为nodeId集合，用于UI加载状态显示
  aiErrorNodeIds: Set<string>; // AI解释请求失败的节点ID，用于错误状态显示
  // 匹配相关状态
  matches: MatchResult[]; // 当前匹配结果
  matchResponse: MatchResponse | null; // 完整的匹配响应（包含错误信息）
}

interface RegexActions {
  /** 设置正则表达式字符串并触发解析 */
  setRegexString: (regex: string) => void;
  /** 设置测试字符串 */
  setTestString: (text: string) => void;
  /** 内部调用，由 setRegexString 触发，生成 AST 和流程图 */
  _generateAstAndFlow: () => Promise<void>;
  /** 获取指定节点的 AI 解释 */
  _fetchExplanationForNode: (nodeId: string, nodeContent: string) => Promise<void>;
  /** 重试获取指定节点的 AI 解释 */
  retryExplanationForNode: (nodeId: string, nodeContent: string) => Promise<void>;
  /** 设置当前悬停的元素 ID */
  setHoveredElementId: (id: string | null) => void;
  /** 从错误状态恢复到最后有效状态 */
  recoverFromError: () => void;
  /** 清除当前错误信息 */
  clearError: () => void;
  /** 设置加载状态 */
  setLoading: (loading: boolean) => void;
  /** 更新匹配结果 */
  _updateMatches: () => void;
}

/**
 * 创建版本管理器
 * 使用闭包封装版本号，避免全局变量竞态条件
 * @returns 版本管理器对象，包含 next() 和 current() 方法
 */
const createVersionManager = () => {
  let version = 0;
  return {
    /** 递增并返回新版本号 */
    next: () => ++version,
    /** 获取当前版本号 */
    current: () => version
  };
};

const versionManager = createVersionManager();

// 使用统一配置
const DEBOUNCE_DELAY = DEBOUNCE_CONFIG.DELAY;
const CACHE_LIMIT = AI_CACHE_CONFIG.LIMIT;
const CACHE_TTL = AI_CACHE_CONFIG.TTL;

/**
 * 生成缓存键，基于 nodeContent 而不是 nodeId
 * 这样相同的正则片段可以共享缓存结果
 * @param nodeContent 正则表达式片段
 * @param flags 可选的正则标志
 * @returns 缓存键
 */
const generateCacheKey = (nodeContent: string, flags?: string): string => {
  return flags ? `${nodeContent}::${flags}` : nodeContent;
};

// 创建 Zustand store
export const useRegexStore = create<RegexState & RegexActions>((set, get) => {
  // 创建错误恢复管理器
  const errorRecovery = new ErrorRecoveryManager();

  // 创建防抖版本的 _generateAstAndFlow
  /**
   * 防抖的 AST 生成和流程图布局函数
   * 避免用户快速输入时的频繁计算，提升性能
   */
  const debouncedGenerateAstAndFlow = createDebounce(async () => {
    const state = get();
    const { regexString, requestVersion } = state;

    // 使用已经在 setRegexString 中设置的版本号，不再重复递增
    const localVersion = requestVersion;
    set({ isLoading: true, error: null });

    // 创建性能计时器
    const timer = new PerformanceTimer('regex-processing');
    const startTime = Date.now();

    try {
      // 保存当前状态快照（如果有有效的 AST）
      if (state.ast) {
        errorRecovery.saveSnapshot({
          regexString: state.regexString,
          testString: state.testString,
          ast: state.ast,
          flowNodes: state.flowNodes,
          flowEdges: state.flowEdges
        });
      }

      // a. 首先调用 parseRegex
      timer.mark('parse-start');
      const parseResult = parseRegex(regexString);
      timer.mark('parse-end');

      // 检查解析是否成功
      if (!parseResult || parseResult.error || !parseResult.ast) {
        // DEBUG: 记录解析失败
        logger.error('正则表达式解析失败，创建错误占位符', {
          regexString,
          parseError: parseResult?.error,
          hasParseResult: !!parseResult,
          hasAst: !!(parseResult?.ast)
        });

        const enhancedError = ErrorHandler.createEnhancedError(
          (parseResult && parseResult.error) || t('errors.regexParseFailure'),
          ErrorType.PARSE_ERROR,
          { regexString, operation: 'parse' }
        );

        const errorPlaceholder = createErrorStatePlaceholder(enhancedError);

        set({
          ast: null,
          flowNodes: errorPlaceholder.nodes,
          flowEdges: errorPlaceholder.edges,
          error: enhancedError,
          isLoading: false
        });
        return;
      }

      // b. 如果 AST 有效，则 await 调用 getLayoutedElements(ast) 来获取最终的 nodes 和 edges
      timer.mark('layout-start');
      const layoutResult = await getLayoutedElements(parseResult.ast);
      timer.mark('layout-end');

      // 并发检查：如果请求版本已过期，丢弃结果
      if (get().requestVersion !== localVersion) {
        set({ isLoading: false }); // 修复状态泄漏
        return;
      }

      // 计算性能指标
      const parseTime = timer.getTimeBetweenMarks('parse-start', 'parse-end');
      const layoutTime = timer.getTimeBetweenMarks('layout-start', 'layout-end');
      const totalTime = Date.now() - startTime;
      const nodeCount = layoutResult.nodes.length;
      const edgeCount = layoutResult.edges.length;
      const complexity = calculateComplexity(nodeCount, edgeCount, regexString.length);
      const memoryUsage = getMemoryUsage();

      // 记录性能指标
      performanceMonitor.recordMetrics({
        parseTime,
        layoutTime,
        totalTime,
        nodeCount,
        edgeCount,
        complexity,
        regexLength: regexString.length,
        memoryUsage,
      });

      // c. 使用 set 函数，用获取到的结果更新 flowNodes 和 flowEdges 状态
      set({
        ast: parseResult.ast,
        flowNodes: layoutResult.nodes,
        flowEdges: layoutResult.edges,
        error: null,
        isLoading: false
      });

    } catch (error) {
      // d. 在 catch 块中，捕获任何错误，更新 error 状态，并清空 flowNodes 和 flowEdges

      const enhancedError = ErrorHandler.createEnhancedError(
        error instanceof Error ? error : t('errors.layoutCalculationFailed'),
        ErrorType.LAYOUT_ERROR,
        { regexString, operation: 'layout' },
        ErrorHandler.isRecoverable(error instanceof Error ? error : t('errors.layoutCalculationFailed'))
      );

      const errorPlaceholder = createErrorStatePlaceholder(enhancedError);

      set({
        ast: null,
        flowNodes: errorPlaceholder.nodes,
        flowEdges: errorPlaceholder.edges,
        error: enhancedError,
        isLoading: false
      });
    }
  }, DEBOUNCE_DELAY); // 可配置的防抖延迟

  // 默认示例正则表达式
  const defaultRegex = '^[a-zA-Z0-9]+$';



  return {
    // 初始状态 - 使用示例驱动的方式
    regexString: defaultRegex,
    testString: 'hello123',
    ast: null,
    flowNodes: [],
    flowEdges: [],
    aiExplanations: new LRUCache<string, string>({ maxSize: CACHE_LIMIT, ttl: CACHE_TTL }),
    hoveredElementId: null,
    error: null,
    isLoading: false,
    lastValidState: null,
    requestVersion: 0,
    pendingExplanations: new Set<string>(),
    pendingNodeIds: new Map<string, Set<string>>(),
    aiErrorNodeIds: new Set<string>(),
    // 匹配相关初始状态
    matches: [],
    matchResponse: null,

    // Actions
    setRegexString: (regex: string) => {
      // 对所有 setRegexString 调用（含非空串）在进入防抖前就递增 versionManager
      // 彻底规避旧请求覆盖新结果的竞态问题
      const newVersion = versionManager.next();
      set({ regexString: regex, error: null, requestVersion: newVersion });

      if (regex.trim()) {
        // 有内容时调用防抖函数，版本号已经在上面递增
        debouncedGenerateAstAndFlow();
      } else {
        // 空字符串时立即处理 - 显示完全空白状态
        set({
          ast: null,
          flowNodes: [],
          flowEdges: [],
          error: null,
          isLoading: false, // 确保清除加载状态，防止闪烁
        });

        // 清空性能监控记录
        performanceMonitor.clearHistory();
      }

      // 更新匹配结果
      get()._updateMatches();
    },

    setTestString: (text: string) => {
      set({ testString: text });
      // 立即更新匹配结果
      get()._updateMatches();
    },

    /**
     * 生成 AST 和流程图布局（内部方法）
     * 使用防抖机制避免频繁计算
     * @returns Promise<void>
     */
    _generateAstAndFlow: async (): Promise<void> => {
      await debouncedGenerateAstAndFlow();
    },

    setLoading: (loading: boolean) => {
      set({ isLoading: loading });
    },

    clearError: () => {
      set({ error: null });
    },

    recoverFromError: () => {
      const lastValid = errorRecovery.getLastValidSnapshot();
      if (lastValid) {
        set({
          regexString: lastValid.regexString,
          testString: lastValid.testString,
          ast: lastValid.ast,
          flowNodes: lastValid.flowNodes,
          flowEdges: lastValid.flowEdges,
          error: null,
          isLoading: false,
          lastValidState: lastValid
        });
      }
    },

    _fetchExplanationForNode: async (nodeId: string, nodeContent: string) => {
      const state = get();
      const { aiExplanations } = state;

      // 2.5 缓存键改为基于 nodeContent，避免同片段多次请求
      const cacheKey = generateCacheKey(nodeContent);

      // 如果已经有解释，则不重复请求
      if (aiExplanations.has(cacheKey)) {
        return; // 优化：统一使用 cacheKey，避免重复存储同一解释
      }

      // 检查是否已经有 nodeId 的直接映射（兼容旧数据）
      if (aiExplanations.has(nodeId)) {
        return;
      }

      // 2. 并发去重优化：100% 消除重复请求的可能性
      let shouldProceed = false;
      set(prev => {
        if (prev.pendingExplanations.has(cacheKey)) {
          return prev; // 二次校验，如果已存在则不修改状态
        }
        shouldProceed = true; // 标记可以继续执行
        const newPendingExplanations = new Set(prev.pendingExplanations);
        const newPendingNodeIds = new Map(prev.pendingNodeIds);
        newPendingExplanations.add(cacheKey);

        // 优化：支持多节点共享同一cacheKey的Loading状态
        if (!newPendingNodeIds.has(cacheKey)) {
          newPendingNodeIds.set(cacheKey, new Set());
        }
        newPendingNodeIds.get(cacheKey)!.add(nodeId);

        return {
          pendingExplanations: newPendingExplanations,
          pendingNodeIds: newPendingNodeIds
        };
      });

      // 如果二次校验发现已有请求在进行，直接返回
      if (!shouldProceed) {
        return;
      }

      try {

        // b. 在 try...catch 块中，使用 fetch 函数向 /api/explain 端点发起 POST 请求
        const response = await fetch('/api/explain', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          // c. 在请求的 body 中，发送包含 { snippet: nodeContent } 的 JSON 字符串
          body: JSON.stringify({ snippet: nodeContent }),
        });

        // 2.2 先校验 response.ok，异常时抛出错误
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // d. 获取响应，并将其解析为 JSON
        const data = await response.json();

        // e. 从解析后的 JSON 中提取 explanation 文本
        const explanation = data.explanation;

        // f. 使用 set 函数，将获取到的解释更新到 aiExplanations 中
        // 1. 修复：克隆缓存并更新，确保 Zustand 触发重渲染
        const newExplanations = aiExplanations.clone();
        newExplanations.set(cacheKey, explanation);
        // 同时设置 nodeId 的映射（如果不同）
        if (nodeId !== cacheKey) {
          newExplanations.set(nodeId, explanation);
        }

        // 1. 并发安全强化：使用函数式更新，确保基于最新状态
        set(prev => {
          const newPendingExplanations = new Set(prev.pendingExplanations);
          const newPendingNodeIds = new Map(prev.pendingNodeIds);
          const newAiErrorNodeIds = new Set(prev.aiErrorNodeIds);
          newPendingExplanations.delete(cacheKey);

          // 优化：清理完成的节点ID，如果该cacheKey下没有其他节点则删除整个条目
          if (newPendingNodeIds.has(cacheKey)) {
            const nodeSet = newPendingNodeIds.get(cacheKey)!;
            nodeSet.delete(nodeId);
            if (nodeSet.size === 0) {
              newPendingNodeIds.delete(cacheKey);
            }
          }

          newAiErrorNodeIds.delete(nodeId); // 成功时清除错误状态
          return {
            aiExplanations: newExplanations,
            pendingExplanations: newPendingExplanations,
            pendingNodeIds: newPendingNodeIds,
            aiErrorNodeIds: newAiErrorNodeIds
          };
        });

      } catch (error) {
        // g. 在 catch 块中，打印错误信息

        // 设置错误标记，避免无限重试
        const errorMessage = t('errors.aiExplanationFailed');

        // 1. 修复：克隆缓存并更新，确保 Zustand 触发重渲染
        const newExplanationsError = aiExplanations.clone();
        newExplanationsError.set(cacheKey, errorMessage);
        // 同时设置 nodeId 的映射（如果不同）
        if (nodeId !== cacheKey) {
          newExplanationsError.set(nodeId, errorMessage);
        }

        // 1. 并发安全强化：使用函数式更新，确保基于最新状态
        set(prev => {
          const newPendingExplanations = new Set(prev.pendingExplanations);
          const newPendingNodeIds = new Map(prev.pendingNodeIds);
          const newAiErrorNodeIds = new Set(prev.aiErrorNodeIds);
          newPendingExplanations.delete(cacheKey);

          // 优化：清理失败的节点ID，如果该cacheKey下没有其他节点则删除整个条目
          if (newPendingNodeIds.has(cacheKey)) {
            const nodeSet = newPendingNodeIds.get(cacheKey)!;
            nodeSet.delete(nodeId);
            if (nodeSet.size === 0) {
              newPendingNodeIds.delete(cacheKey);
            }
          }

          newAiErrorNodeIds.add(nodeId); // 错误时添加到错误状态
          return {
            aiExplanations: newExplanationsError,
            pendingExplanations: newPendingExplanations,
            pendingNodeIds: newPendingNodeIds,
            aiErrorNodeIds: newAiErrorNodeIds
          };
        });
      }
    },

    setHoveredElementId: (id: string | null) => {
      set({ hoveredElementId: id });
    },

    retryExplanationForNode: async (nodeId: string, nodeContent: string) => {
      const cacheKey = generateCacheKey(nodeContent);

      // 清除错误状态，允许重新请求
      set(prev => {
        const newAiErrorNodeIds = new Set(prev.aiErrorNodeIds);
        newAiErrorNodeIds.delete(nodeId);

        // 直接删除错误的缓存项，保留其他缓存
        prev.aiExplanations.delete(cacheKey);

        return {
          aiErrorNodeIds: newAiErrorNodeIds
        };
      });

      // 重新请求解释
      return get()._fetchExplanationForNode(nodeId, nodeContent);
    },

    // 更新匹配结果
    _updateMatches: () => {
      const state = get();
      const { regexString, testString } = state;

      // 获取匹配结果
      const matchResponse = getMatches(regexString, testString);

      // 更新状态
      set({
        matches: matchResponse.matches,
        matchResponse: matchResponse
      });
    },

    // 初始化函数 - 处理默认正则表达式
    _initialize: () => {
      const state = get();



      if (state.regexString && !state.ast) {
        // 自动处理默认正则表达式
        debouncedGenerateAstAndFlow();
      }
      // 初始化时也更新匹配结果
      get()._updateMatches();
    }
  };
});