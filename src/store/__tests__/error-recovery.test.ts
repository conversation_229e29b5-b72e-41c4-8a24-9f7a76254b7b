import { describe, it, expect, beforeEach } from 'vitest';
import { 
  <PERSON>rrorRecoveryManager, 
  ErrorHandler, 
  ErrorType,
  createEmptyStatePlaceholder,
  createErrorStatePlaceholder
} from '../error-recovery';

describe('ErrorRecoveryManager', () => {
  let manager: ErrorRecoveryManager;

  beforeEach(() => {
    manager = new ErrorRecoveryManager();
  });

  describe('saveSnapshot', () => {
    it('should save a snapshot', () => {
      const snapshot = {
        regexString: 'abc',
        testString: 'test',
        ast: null,
        flowNodes: [],
        flowEdges: []
      };

      manager.saveSnapshot(snapshot);
      const snapshots = manager.getAllSnapshots();
      
      expect(snapshots).toHaveLength(1);
      expect(snapshots[0].regexString).toBe('abc');
      expect(snapshots[0].timestamp).toBeTypeOf('number');
    });

    it('should limit snapshots to maximum count', () => {
      // Save more than max snapshots (5)
      for (let i = 0; i < 7; i++) {
        manager.saveSnapshot({
          regexString: `regex${i}`,
          testString: '',
          ast: null,
          flowNodes: [],
          flowEdges: []
        });
      }

      const snapshots = manager.getAllSnapshots();
      expect(snapshots).toHaveLength(5);
      expect(snapshots[0].regexString).toBe('regex2'); // First two should be removed
    });
  });

  describe('getLastValidSnapshot', () => {
    it('should return null when no valid snapshots exist', () => {
      const result = manager.getLastValidSnapshot();
      expect(result).toBeNull();
    });

    it('should return the most recent valid snapshot', () => {
      // Save invalid snapshot (no AST or nodes)
      manager.saveSnapshot({
        regexString: 'invalid',
        testString: '',
        ast: null,
        flowNodes: [],
        flowEdges: []
      });

      // Save valid snapshot
      manager.saveSnapshot({
        regexString: 'valid',
        testString: '',
        ast: { type: 'RegExp' } as any,
        flowNodes: [{ id: 'node1' } as any],
        flowEdges: []
      });

      const result = manager.getLastValidSnapshot();
      expect(result?.regexString).toBe('valid');
    });
  });

  describe('clearExpiredSnapshots', () => {
    it('should remove expired snapshots', () => {
      const oldTimestamp = Date.now() - 10 * 60 * 1000; // 10 minutes ago
      
      // Manually add old snapshot
      (manager as any).snapshots.push({
        regexString: 'old',
        testString: '',
        ast: null,
        flowNodes: [],
        flowEdges: [],
        timestamp: oldTimestamp
      });

      // Add recent snapshot
      manager.saveSnapshot({
        regexString: 'recent',
        testString: '',
        ast: null,
        flowNodes: [],
        flowEdges: []
      });

      manager.clearExpiredSnapshots(5 * 60 * 1000); // 5 minutes max age
      
      const snapshots = manager.getAllSnapshots();
      expect(snapshots).toHaveLength(1);
      expect(snapshots[0].regexString).toBe('recent');
    });
  });
});

describe('ErrorHandler', () => {
  describe('createEnhancedError', () => {
    it('should create enhanced error from string', () => {
      const error = ErrorHandler.createEnhancedError(
        'Test error message',
        ErrorType.PARSE_ERROR,
        { regexString: 'abc' }
      );

      expect(error.type).toBe(ErrorType.PARSE_ERROR);
      expect(error.message).toBe('Test error message');
      expect(error.context?.regexString).toBe('abc');
      expect(error.recoverable).toBe(true);
      expect(error.suggestions).toBeInstanceOf(Array);
      expect(error.timestamp).toBeTypeOf('number');
    });

    it('should create enhanced error from Error object', () => {
      const originalError = new Error('Original error');
      const error = ErrorHandler.createEnhancedError(
        originalError,
        ErrorType.LAYOUT_ERROR
      );

      expect(error.message).toBe('Original error');
      expect(error.originalError).toBe(originalError);
      expect(error.type).toBe(ErrorType.LAYOUT_ERROR);
    });

    it('should provide appropriate suggestions for parse errors', () => {
      const error = ErrorHandler.createEnhancedError(
        'Unterminated group',
        ErrorType.PARSE_ERROR
      );

      expect(error.suggestions).toContain('检查是否有未闭合的括号或方括号');
    });

    it('should provide appropriate suggestions for layout errors', () => {
      const error = ErrorHandler.createEnhancedError(
        'Layout failed',
        ErrorType.LAYOUT_ERROR
      );

      expect(error.suggestions).toContain('尝试简化正则表达式结构');
    });
  });

  describe('isRecoverable', () => {
    it('should return true for recoverable errors', () => {
      expect(ErrorHandler.isRecoverable('Parse error')).toBe(true);
      expect(ErrorHandler.isRecoverable(new Error('Layout error'))).toBe(true);
    });

    it('should return false for unrecoverable errors', () => {
      expect(ErrorHandler.isRecoverable('out of memory')).toBe(false);
      expect(ErrorHandler.isRecoverable('maximum call stack')).toBe(false);
    });
  });
});

describe('Placeholder Functions', () => {
  describe('createEmptyStatePlaceholder', () => {
    it('should create empty state placeholder', () => {
      const result = createEmptyStatePlaceholder();
      
      expect(result.nodes).toHaveLength(1);
      expect(result.edges).toHaveLength(0);
      expect(result.nodes[0].id).toBe('empty-placeholder');
      expect(result.nodes[0].data.type).toBe('placeholder');
    });
  });

  describe('createErrorStatePlaceholder', () => {
    it('should create error state placeholder', () => {
      const error = ErrorHandler.createEnhancedError(
        'Test error',
        ErrorType.PARSE_ERROR
      );
      
      const result = createErrorStatePlaceholder(error);
      
      expect(result.nodes).toHaveLength(1);
      expect(result.edges).toHaveLength(0);
      expect(result.nodes[0].id).toBe('error-placeholder');
      expect(result.nodes[0].data.type).toBe('error');
      expect(result.nodes[0].data.recoverable).toBe(true);
    });

    it('should use different styling for unrecoverable errors', () => {
      const error = ErrorHandler.createEnhancedError(
        'Fatal error',
        ErrorType.UNKNOWN_ERROR,
        undefined,
        false // not recoverable
      );
      
      const result = createErrorStatePlaceholder(error);
      const node = result.nodes[0];
      
      expect(node.data.recoverable).toBe(false);
      expect(node.style?.border).toContain('#ef4444'); // Red color for fatal errors
    });
  });
});
