import type { Node, Edge } from '@xyflow/react';
import type { AstRegExp } from 'regexp-tree/ast';
import { t } from '../lib/i18n';
import { logger } from '../lib/logger';
import { PERFORMANCE_CONFIG } from '../lib/config';

/**
 * Store 状态快照接口
 */
export interface StoreSnapshot {
  regexString: string;
  testString: string;
  ast: AstRegExp | null;
  flowNodes: Node[];
  flowEdges: Edge[];
  timestamp: number;
}

/**
 * 错误恢复管理器
 */
export class ErrorRecoveryManager {
  private snapshots: StoreSnapshot[] = [];
  private maxSnapshots = PERFORMANCE_CONFIG.SNAPSHOT_LIMIT; // 使用配置化的快照上限

  /**
   * 保存状态快照
   */
  saveSnapshot(snapshot: Omit<StoreSnapshot, 'timestamp'>): void {
    const timestampedSnapshot: StoreSnapshot = {
      ...snapshot,
      timestamp: Date.now()
    };

    this.snapshots.push(timestampedSnapshot);

    // 保持快照数量在限制内
    if (this.snapshots.length > this.maxSnapshots) {
      this.snapshots.shift();
    }
  }

  /**
   * 获取最后一个有效的快照
   */
  getLastValidSnapshot(): StoreSnapshot | null {
    // 返回最近的有效快照（有 AST 且有节点的）
    for (let i = this.snapshots.length - 1; i >= 0; i--) {
      const snapshot = this.snapshots[i];
      if (snapshot.ast && snapshot.flowNodes.length > 0) {
        return snapshot;
      }
    }
    return null;
  }

  /**
   * 获取所有快照
   */
  getAllSnapshots(): StoreSnapshot[] {
    return [...this.snapshots];
  }

  /**
   * 清除所有快照
   */
  clearSnapshots(): void {
    this.snapshots = [];
  }

  /**
   * 清除过期快照（超过指定时间的）
   */
  clearExpiredSnapshots(maxAge: number = 5 * 60 * 1000): void {
    const now = Date.now();
    this.snapshots = this.snapshots.filter(
      snapshot => now - snapshot.timestamp < maxAge
    );
  }
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  PARSE_ERROR = 'PARSE_ERROR',
  LAYOUT_ERROR = 'LAYOUT_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 增强的错误信息接口
 */
export interface EnhancedError {
  type: ErrorType;
  message: string;
  originalError?: Error;
  timestamp: number;
  context?: {
    regexString?: string;
    operation?: string;
    [key: string]: any;
  };
  recoverable: boolean;
  suggestions?: string[];
}

/**
 * 错误处理工具函数
 */
export class ErrorHandler {
  /**
   * 创建增强的错误信息
   */
  static createEnhancedError(
    error: Error | string,
    type: ErrorType,
    context?: EnhancedError['context'],
    recoverable: boolean = true
  ): EnhancedError {
    const rawMessage = typeof error === 'string' ? error : error.message;
    // 限制错误消息长度，防止过长的错误信息
    const message = ErrorHandler.truncateMessage(rawMessage);
    const originalError = typeof error === 'string' ? undefined : error;

    const enhancedError: EnhancedError = {
      type,
      message,
      originalError,
      timestamp: Date.now(),
      context,
      recoverable,
      suggestions: ErrorHandler.getSuggestions(type, message)
    };

    return enhancedError;
  }

  /**
   * 根据错误类型和消息获取建议
   */
  private static getSuggestions(type: ErrorType, message: string): string[] {
    const suggestions: string[] = [];

    switch (type) {
      case ErrorType.PARSE_ERROR:
        suggestions.push(t('suggestions.checkSyntax'));
        if (message.includes('Unterminated')) {
          suggestions.push(t('suggestions.checkBrackets'));
        }
        if (message.includes('Invalid')) {
          suggestions.push(t('suggestions.checkInvalidChars'));
        }
        suggestions.push(t('suggestions.simplifyRegex'));
        break;

      case ErrorType.LAYOUT_ERROR:
        suggestions.push(t('suggestions.simplifyStructure'));
        suggestions.push(t('suggestions.checkNesting'));
        break;

      case ErrorType.NETWORK_ERROR:
        suggestions.push(t('suggestions.checkNetwork'));
        suggestions.push(t('suggestions.retryLater'));
        break;

      default:
        suggestions.push(t('suggestions.refreshPage'));
        suggestions.push(t('suggestions.contactSupport'));
    }

    return suggestions;
  }

  /**
   * 判断错误是否可恢复
   */
  static isRecoverable(error: Error | string): boolean {
    const message = typeof error === 'string' ? error : error.message;

    // 某些严重错误不可恢复
    const unrecoverablePatterns = [
      'out of memory',
      'maximum call stack',
      'script error'
    ];

    return !unrecoverablePatterns.some(pattern =>
      message.toLowerCase().includes(pattern)
    );
  }

  /**
   * 截断错误消息，防止过长的错误信息
   */
  static truncateMessage(message: string, maxLength: number = 500): string {
    if (message.length <= maxLength) {
      return message;
    }

    // 截断并添加省略号
    return message.substring(0, maxLength - 3) + '...';
  }
}

/**
 * 创建友好的空状态占位符
 */
export function createEmptyStatePlaceholder(): { nodes: Node[]; edges: Edge[] } {
  return {
    nodes: [{
      id: 'empty-placeholder',
      type: 'regexNode', // 使用正确的节点类型
      position: { x: 200, y: 100 },
      data: {
        label: t('placeholders.emptyTitle'),
        content: t('placeholders.emptyDescription'),
        icon: '🔍',
        type: 'placeholder'
      },
      style: {
        width: 280,
        height: 80,
        backgroundColor: '#f8fafc',
        border: '2px dashed #cbd5e1',
        borderRadius: '12px',
        color: '#64748b',
        fontSize: '14px',
        textAlign: 'center',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px'
      }
    }],
    edges: []
  };
}

/**
 * 创建错误状态占位符
 */
export function createErrorStatePlaceholder(error: EnhancedError): { nodes: Node[]; edges: Edge[] } {


  const errorIcon = error.type === ErrorType.PARSE_ERROR ? '⚠️' : '❌';
  const errorColor = error.recoverable ? '#f59e0b' : '#ef4444';

  // 根据窗口大小居中显示，避免在窄屏幕上被遮挡
  const centerX = typeof window !== 'undefined' ? Math.max(200, window.innerWidth / 2 - 160) : 200;
  const centerY = typeof window !== 'undefined' ? Math.max(100, window.innerHeight / 2 - 50) : 100;

  return {
    nodes: [{
      id: 'error-placeholder',
      type: 'regexNode', // 使用正确的节点类型
      position: { x: centerX, y: centerY },
      data: {
        label: `${errorIcon} ${t('placeholders.errorTitle')}`,
        content: error.message,
        suggestions: error.suggestions,
        type: 'error',
        recoverable: error.recoverable
      },
      style: {
        width: 320,
        height: 100,
        backgroundColor: '#fef2f2',
        border: `2px solid ${errorColor}`,
        borderRadius: '12px',
        color: errorColor,
        fontSize: '14px',
        textAlign: 'center',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '16px'
      }
    }],
    edges: []
  };
}
