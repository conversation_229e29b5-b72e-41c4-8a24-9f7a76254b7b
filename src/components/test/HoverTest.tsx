"use client"

import { useRegexStore } from "@/store/regex.store"
import { useTranslation } from "@/lib/i18n"

/**
 * HoverTest 组件 - 悬停状态调试面板
 *
 * 功能：
 * - 实时显示当前悬停的元素ID
 * - 展示 AI 解释缓存状态和统计信息
 * - 显示当前悬停元素的解释内容
 *
 * 注意：此组件仅用于开发调试，生产环境应动态导入
 */
export default function HoverTest() {
  // 使用 selector 优化性能，仅在需要的字段变化时重新渲染
  const hoveredElementId = useRegexStore(state => state.hoveredElementId)
  const aiExplanations = useRegexStore(state => state.aiExplanations)

  // 若迁至正式 UI：用 selector 直接取 currentExplanation，减少重渲染
  const currentExplanation = useRegexStore(state =>
    state.hoveredElementId ? state.aiExplanations.get(state.hoveredElementId) : null
  )

  const { t } = useTranslation()

  // 获取缓存统计信息
  const cacheStats = aiExplanations.getStats()

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-semibold mb-2">{t('debug.hoverTestPanel')}</h3>
      <div className="space-y-2">
        <div>
          <strong>{t('debug.currentHoveredElementId')}:</strong>
          <span className="ml-2 text-blue-600">
            {hoveredElementId || t('debug.none')}
          </span>
        </div>
        <div>
          <strong>{t('debug.aiExplanationCacheSize')}:</strong>
          <span className="ml-2 text-green-600">
            {aiExplanations.getSize()}
          </span>
        </div>
        <div>
          <strong>{t('debug.cacheHitRate')}:</strong>
          <span className="ml-2 text-purple-600">
            {cacheStats.hitRate !== undefined && Number.isFinite(cacheStats.hitRate)
              ? `${cacheStats.hitRate.toFixed(2)}%`
              : t('debug.notAvailable')}
          </span>
        </div>
        {hoveredElementId && currentExplanation && (
          <div className="mt-4 p-3 bg-white rounded border">
            <strong>{t('debug.currentHoveredElementExplanation')}:</strong>
            <pre className="mt-2 text-sm text-gray-700 whitespace-pre-wrap">
              {currentExplanation}
            </pre>
          </div>
        )}
        {hoveredElementId && !currentExplanation && (
          <div className="mt-4 p-3 bg-white rounded border">
            <strong>{t('debug.currentHoveredElementExplanation')}:</strong>
            <div className="mt-2 text-sm text-gray-500">
              {t('debug.loading')}...
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
