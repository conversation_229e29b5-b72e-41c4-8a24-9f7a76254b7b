"use client"

import React, { useState } from 'react';
// import { Button } from '../ui/button';
import { useRegexStore } from '../../store/regex.store';

/**
 * API 测试按钮组件
 * 用于测试任务 5.3 的实现
 * 3.2 仅在开发环境渲染
 */
export default function ApiTestButton() {
  // 3.2 仅在开发环境渲染
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [testNodeId] = useState('test-node-' + Date.now());
  const [testNodeContent] = useState('\\d+');
  const [isResultVisible, setIsResultVisible] = useState(true);

  // 4. 修复：订阅 store 状态变化，实时显示解释内容
  const _fetchExplanationForNode = useRegexStore(state => state._fetchExplanationForNode);
  const explanation = useRegexStore(
    state => state.aiExplanations.get(testNodeId) ?? state.aiExplanations.get(testNodeContent)
  );

  // 4. 修复：使用 React effect 监听解释变化
  React.useEffect(() => {
    if (explanation) {
      setResult(`成功! 解释: ${explanation}`);
      setIsResultVisible(true);

      // 2. 体验优化：解释加载完毕后 5 秒自动淡出，避免干扰 UI
      const timer = setTimeout(() => {
        setIsResultVisible(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [explanation]);

  const handleTest = async () => {
    setIsLoading(true);
    setResult(null);
    setIsResultVisible(true);

    try {
          // API测试日志已禁用

      // 调用我们实现的函数
      await _fetchExplanationForNode(testNodeId, testNodeContent);

      // 4. 修复：不再手动检查结果，由 useEffect 处理
      // API调用完成日志已禁用

    } catch (error) {
      console.error('❌ 测试失败:', error);
      setResult(`❌ 测试失败: ${error instanceof Error ? error.message : String(error)}`);
      setIsResultVisible(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-2">API 集成测试</h3>
      <p className="text-sm text-gray-600 mb-4">
        测试任务 5.3: 前端调用 /api/explain 端点
      </p>
      
      <button
        onClick={handleTest}
        disabled={isLoading}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
      >
        {isLoading ? '测试中...' : '测试 API 调用'}
      </button>

      {result && (
        <div
          className={`mt-4 p-3 bg-white border rounded transition-opacity duration-1000 ${
            isResultVisible ? 'opacity-100' : 'opacity-30 hover:opacity-100'
          }`}
        >
          <div className="flex justify-between items-start mb-2">
            <h4 className="font-medium">测试结果:</h4>
            <button
              onClick={() => setIsResultVisible(!isResultVisible)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              {isResultVisible ? '折叠' : '展开'}
            </button>
          </div>
          <p className="text-sm">{result}</p>
        </div>
      )}
    </div>
  );
}
