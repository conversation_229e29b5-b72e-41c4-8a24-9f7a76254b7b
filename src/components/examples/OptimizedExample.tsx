'use client';

import React, { useEffect } from 'react';
import { useOptimizedRegexStore } from '../../hooks/useRegexStore';
import { usePerformanceMonitor } from '../../hooks/usePerformance';
import { useLanguage } from '../../hooks/useLanguage';
import { LanguageSwitcher } from '../ui/LanguageSwitcher';
import { PerformanceMonitor } from '../debug/PerformanceMonitor';

/**
 * 展示优化功能的示例组件
 */
export function OptimizedExample() {
  const {
    regexString,
    setRegexString,
    nodes,
    edges,
    error,
    isLoading,
    hasError,
    isEmpty,
    nodeCount,
    edgeCount,
  } = useOptimizedRegexStore();

  const {
    stats,
    formatMetrics,
    getPerformanceLevel,
  } = usePerformanceMonitor();

  const { currentLocale, toggleLanguage } = useLanguage();

  // 示例正则表达式
  const examples = [
    { 
      label: currentLocale === 'zh-CN' ? '简单字符' : 'Simple Characters', 
      regex: 'abc' 
    },
    { 
      label: currentLocale === 'zh-CN' ? '字符类' : 'Character Class', 
      regex: '[a-z]+' 
    },
    { 
      label: currentLocale === 'zh-CN' ? '量词' : 'Quantifiers', 
      regex: 'a{2,5}' 
    },
    { 
      label: currentLocale === 'zh-CN' ? '分组' : 'Groups', 
      regex: '(abc|def)' 
    },
    { 
      label: currentLocale === 'zh-CN' ? '复杂表达式' : 'Complex Pattern', 
      regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' 
    },
  ];

  // 自动测试性能
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
        // 组件加载日志已禁用
    }
  }, [currentLocale, stats]);

  const formattedStats = stats.latest ? formatMetrics(stats.latest) : null;
  const performanceLevel = stats.latest ? getPerformanceLevel(stats.latest) : null;

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* 头部 */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">
          {currentLocale === 'zh-CN' ? '正则表达式可视化工具' : 'Regex Visualization Tool'}
        </h1>
        <div className="flex items-center space-x-4">
          <LanguageSwitcher variant="toggle" />
          <button
            onClick={toggleLanguage}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            {currentLocale === 'zh-CN' ? 'Switch to English' : '切换到中文'}
          </button>
        </div>
      </div>

      {/* 示例按钮 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">
          {currentLocale === 'zh-CN' ? '示例' : 'Examples'}
        </h2>
        <div className="flex flex-wrap gap-2">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => setRegexString(example.regex)}
              className="px-3 py-2 text-sm bg-blue-100 hover:bg-blue-200 rounded-md transition-colors"
            >
              {example.label}
            </button>
          ))}
        </div>
      </div>

      {/* 输入区域 */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          {currentLocale === 'zh-CN' ? '正则表达式' : 'Regular Expression'}
        </label>
        <input
          type="text"
          value={regexString}
          onChange={(e) => setRegexString(e.target.value)}
          placeholder={currentLocale === 'zh-CN' ? '请输入正则表达式...' : 'Enter regular expression...'}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* 状态显示 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {/* 基本信息 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-600 mb-2">
            {currentLocale === 'zh-CN' ? '基本信息' : 'Basic Info'}
          </h3>
          <div className="space-y-1 text-sm">
            <div>
              <span className="text-gray-500">
                {currentLocale === 'zh-CN' ? '节点数量' : 'Nodes'}:
              </span>
              <span className="ml-2 font-mono">{nodeCount}</span>
            </div>
            <div>
              <span className="text-gray-500">
                {currentLocale === 'zh-CN' ? '边数量' : 'Edges'}:
              </span>
              <span className="ml-2 font-mono">{edgeCount}</span>
            </div>
            <div>
              <span className="text-gray-500">
                {currentLocale === 'zh-CN' ? '状态' : 'Status'}:
              </span>
              <span className={`ml-2 ${isLoading ? 'text-blue-600' : hasError ? 'text-red-600' : isEmpty ? 'text-gray-500' : 'text-green-600'}`}>
                {isLoading 
                  ? (currentLocale === 'zh-CN' ? '加载中...' : 'Loading...') 
                  : hasError 
                    ? (currentLocale === 'zh-CN' ? '错误' : 'Error')
                    : isEmpty 
                      ? (currentLocale === 'zh-CN' ? '空' : 'Empty')
                      : (currentLocale === 'zh-CN' ? '正常' : 'Ready')
                }
              </span>
            </div>
          </div>
        </div>

        {/* 性能信息 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-600 mb-2">
            {currentLocale === 'zh-CN' ? '性能信息' : 'Performance'}
          </h3>
          {formattedStats ? (
            <div className="space-y-1 text-sm">
              <div>
                <span className="text-gray-500">
                  {currentLocale === 'zh-CN' ? '解析时间' : 'Parse Time'}:
                </span>
                <span className="ml-2 font-mono">{formattedStats.parseTime}</span>
              </div>
              <div>
                <span className="text-gray-500">
                  {currentLocale === 'zh-CN' ? '布局时间' : 'Layout Time'}:
                </span>
                <span className="ml-2 font-mono">{formattedStats.layoutTime}</span>
              </div>
              <div>
                <span className="text-gray-500">
                  {currentLocale === 'zh-CN' ? '总时间' : 'Total Time'}:
                </span>
                <span className="ml-2 font-mono">{formattedStats.totalTime}</span>
              </div>
              {performanceLevel && (
                <div>
                  <span className="text-gray-500">
                    {currentLocale === 'zh-CN' ? '性能等级' : 'Performance'}:
                  </span>
                  <span 
                    className="ml-2 font-medium"
                    style={{ color: performanceLevel.color }}
                  >
                    {performanceLevel.label}
                  </span>
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm text-gray-500">
              {currentLocale === 'zh-CN' ? '暂无数据' : 'No data'}
            </div>
          )}
        </div>

        {/* 错误信息 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-600 mb-2">
            {currentLocale === 'zh-CN' ? '错误信息' : 'Error Info'}
          </h3>
          {error ? (
            <div className="space-y-2">
              <div className="text-sm text-red-600">
                {error.message}
              </div>
              {error.suggestions && error.suggestions.length > 0 && (
                <div className="text-xs text-gray-600">
                  <div className="font-medium mb-1">
                    {currentLocale === 'zh-CN' ? '建议' : 'Suggestions'}:
                  </div>
                  <ul className="list-disc list-inside space-y-1">
                    {error.suggestions.slice(0, 2).map((suggestion, index) => (
                      <li key={index}>{suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm text-gray-500">
              {currentLocale === 'zh-CN' ? '无错误' : 'No errors'}
            </div>
          )}
        </div>
      </div>

      {/* 可视化区域占位符 */}
      <div className="bg-white border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
        <div className="text-gray-500">
          {currentLocale === 'zh-CN' 
            ? '这里将显示正则表达式的可视化图表' 
            : 'Regex visualization chart will be displayed here'
          }
        </div>
        <div className="text-sm text-gray-400 mt-2">
          {currentLocale === 'zh-CN' 
            ? `当前有 ${nodeCount} 个节点和 ${edgeCount} 条边` 
            : `Currently ${nodeCount} nodes and ${edgeCount} edges`
          }
        </div>
      </div>

      {/* 性能监控组件（仅开发环境） */}
      <PerformanceMonitor showDetails={true} />
    </div>
  );
}
