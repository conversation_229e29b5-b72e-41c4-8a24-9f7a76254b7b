'use client';

import React from 'react';
import { usePerformanceMonitor, usePerformanceWarnings } from '../../hooks/usePerformance';
import { useLanguage } from '../../hooks/useLanguage';

interface PerformanceMonitorProps {
  className?: string;
  showDetails?: boolean;
}

/**
 * 性能监控组件
 */
export function PerformanceMonitor({ className = '', showDetails = false }: PerformanceMonitorProps) {
  const {
    stats,
    isVisible,
    formatMetrics,
    getPerformanceLevel,
    toggleVisibility,
    clearHistory,
    t,
  } = usePerformanceMonitor();

  const { warnings, hasWarnings } = usePerformanceWarnings();
  const { currentLocale } = useLanguage();

  // 只在开发环境中显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const formattedLatest = stats.latest ? formatMetrics(stats.latest) : null;
  const formattedAverage = formatMetrics(stats.average);
  const performanceLevel = stats.latest ? getPerformanceLevel(stats.latest) : null;

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
      {/* 性能指示器 */}
      <div
        className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border cursor-pointer"
        onClick={toggleVisibility}
      >
        <div className="p-3 flex items-center space-x-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: performanceLevel?.color || '#6b7280' }}
          />
          <span className="text-sm font-medium">
            {performanceLevel?.label || t('performance.totalTime')}
          </span>
          {formattedLatest && (
            <span className="text-xs text-gray-500">
              {formattedLatest.totalTime}
            </span>
          )}
          {hasWarnings && (
            <span className="text-xs text-red-500">⚠️</span>
          )}
        </div>
      </div>

      {/* 详细面板 */}
      {isVisible && (
        <div className="mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 w-80">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold">
              {t('performance.totalTime')} {currentLocale === 'zh-CN' ? '监控' : 'Monitor'}
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={clearHistory}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                {currentLocale === 'zh-CN' ? '清除' : 'Clear'}
              </button>
              <button
                onClick={toggleVisibility}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
          </div>

          {/* 当前性能 */}
          {formattedLatest && (
            <div className="mb-4">
              <h4 className="text-xs font-medium text-gray-600 mb-2">
                {currentLocale === 'zh-CN' ? '当前性能' : 'Current Performance'}
              </h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-500">{t('performance.parseTime')}:</span>
                  <span className="ml-1 font-mono">{formattedLatest.parseTime}</span>
                </div>
                <div>
                  <span className="text-gray-500">{t('performance.layoutTime')}:</span>
                  <span className="ml-1 font-mono">{formattedLatest.layoutTime}</span>
                </div>
                <div>
                  <span className="text-gray-500">{t('performance.nodeCount')}:</span>
                  <span className="ml-1 font-mono">{formattedLatest.nodeCount}</span>
                </div>
                <div>
                  <span className="text-gray-500">{t('performance.edgeCount')}:</span>
                  <span className="ml-1 font-mono">{formattedLatest.edgeCount}</span>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-500">{t('performance.complexity')}:</span>
                  <span className="ml-1 font-mono">{formattedLatest.complexity}</span>
                </div>
              </div>
            </div>
          )}

          {/* 平均性能 */}
          {stats.count > 1 && (
            <div className="mb-4">
              <h4 className="text-xs font-medium text-gray-600 mb-2">
                {currentLocale === 'zh-CN' ? '平均性能' : 'Average Performance'} ({stats.count})
              </h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-gray-500">{t('performance.parseTime')}:</span>
                  <span className="ml-1 font-mono">{formattedAverage.parseTime}</span>
                </div>
                <div>
                  <span className="text-gray-500">{t('performance.layoutTime')}:</span>
                  <span className="ml-1 font-mono">{formattedAverage.layoutTime}</span>
                </div>
                <div>
                  <span className="text-gray-500">{t('performance.nodeCount')}:</span>
                  <span className="ml-1 font-mono">{formattedAverage.nodeCount}</span>
                </div>
                <div>
                  <span className="text-gray-500">{t('performance.edgeCount')}:</span>
                  <span className="ml-1 font-mono">{formattedAverage.edgeCount}</span>
                </div>
              </div>
            </div>
          )}

          {/* 性能警告 */}
          {hasWarnings && (
            <div className="mb-4">
              <h4 className="text-xs font-medium text-red-600 mb-2">
                ⚠️ {currentLocale === 'zh-CN' ? '性能警告' : 'Performance Warnings'}
              </h4>
              <div className="space-y-1">
                {warnings.map((warning, index) => (
                  <div key={index} className="text-xs text-red-500 bg-red-50 p-2 rounded">
                    {warning}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 详细信息 */}
          {showDetails && formattedLatest && (
            <div>
              <h4 className="text-xs font-medium text-gray-600 mb-2">
                {currentLocale === 'zh-CN' ? '详细信息' : 'Details'}
              </h4>
              <div className="text-xs space-y-1">
                <div>
                  <span className="text-gray-500">Memory:</span>
                  <span className="ml-1 font-mono">{formattedLatest.memoryUsage}</span>
                </div>
                <div>
                  <span className="text-gray-500">Regex Length:</span>
                  <span className="ml-1 font-mono">{formattedLatest.regexLength}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * 简化的性能指示器
 */
export function PerformanceIndicator({ className = '' }: { className?: string }) {
  const { stats } = usePerformanceMonitor();
  const { hasWarnings } = usePerformanceWarnings();

  // 只在开发环境中显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const performanceLevel = stats.latest ? 
    (() => {
      const totalTime = stats.latest!.totalTime;
      if (totalTime > 2000) return { color: '#ef4444', label: '慢' };
      if (totalTime > 1000) return { color: '#f59e0b', label: '中' };
      if (totalTime > 500) return { color: '#10b981', label: '快' };
      return { color: '#06b6d4', label: '极快' };
    })() : null;

  return (
    <div className={`inline-flex items-center space-x-1 ${className}`}>
      {performanceLevel && (
        <>
          <div
            className="w-2 h-2 rounded-full"
            style={{ backgroundColor: performanceLevel.color }}
          />
          <span className="text-xs text-gray-500">
            {stats.latest?.totalTime}ms
          </span>
        </>
      )}
      {hasWarnings && (
        <span className="text-xs text-red-500">⚠️</span>
      )}
    </div>
  );
}
