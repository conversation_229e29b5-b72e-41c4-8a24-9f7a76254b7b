/**
 * 高亮文本渲染组件
 * 用于在文本中高亮显示匹配的部分
 */

import React from 'react';
import { cn } from '@/lib/utils';
import type { MatchResult } from '@/lib/matcher';

interface HighlightedTextProps {
  /** 原始文本 */
  text: string;
  /** 匹配结果数组 */
  matches: MatchResult[];
  /** 自定义样式类名 */
  className?: string;
  /** 高亮部分的样式类名 */
  highlightClassName?: string;
  /** 非高亮部分的样式类名 */
  normalClassName?: string;
  /** 是否显示匹配索引 */
  showMatchIndex?: boolean;
}

/**
 * 文本片段类型
 */
interface TextSegment {
  text: string;
  isHighlighted: boolean;
  matchIndex?: number;
  startIndex: number;
  endIndex: number;
}

/**
 * 将文本和匹配结果转换为文本片段数组
 */
function createTextSegments(text: string, matches: MatchResult[]): TextSegment[] {
  if (!text || matches.length === 0) {
    return [{
      text,
      isHighlighted: false,
      startIndex: 0,
      endIndex: text.length
    }];
  }

  // 按开始位置排序匹配结果
  const sortedMatches = [...matches].sort((a, b) => a.startIndex - b.startIndex);
  
  const segments: TextSegment[] = [];
  let currentIndex = 0;

  for (const match of sortedMatches) {
    // 添加匹配前的普通文本
    if (currentIndex < match.startIndex) {
      segments.push({
        text: text.slice(currentIndex, match.startIndex),
        isHighlighted: false,
        startIndex: currentIndex,
        endIndex: match.startIndex
      });
    }

    // 添加高亮的匹配文本
    segments.push({
      text: match.text,
      isHighlighted: true,
      matchIndex: match.matchIndex,
      startIndex: match.startIndex,
      endIndex: match.endIndex
    });

    currentIndex = match.endIndex;
  }

  // 添加最后的普通文本
  if (currentIndex < text.length) {
    segments.push({
      text: text.slice(currentIndex),
      isHighlighted: false,
      startIndex: currentIndex,
      endIndex: text.length
    });
  }

  return segments;
}

/**
 * 高亮文本组件
 */
export default function HighlightedText({
  text,
  matches,
  className,
  highlightClassName = "bg-yellow-400/30 text-yellow-100 border border-yellow-400/50 rounded px-0.5",
  normalClassName = "text-slate-300",
  showMatchIndex = false
}: HighlightedTextProps) {
  const segments = createTextSegments(text, matches);

  return (
    <div className={cn("font-mono text-sm leading-relaxed", className)}>
      {segments.map((segment, index) => {
        if (segment.isHighlighted) {
          return (
            <span
              key={`highlight-${index}-${segment.startIndex}`}
              className={cn(highlightClassName, "relative")}
              title={`匹配 ${segment.matchIndex! + 1}: "${segment.text}"`}
            >
              {segment.text}
              {showMatchIndex && (
                <span className="absolute -top-2 -right-1 text-xs bg-yellow-500 text-black rounded-full w-4 h-4 flex items-center justify-center font-bold">
                  {segment.matchIndex! + 1}
                </span>
              )}
            </span>
          );
        } else {
          return (
            <span
              key={`normal-${index}-${segment.startIndex}`}
              className={normalClassName}
            >
              {segment.text}
            </span>
          );
        }
      })}
    </div>
  );
}

/**
 * 匹配统计信息组件
 */
interface MatchStatsProps {
  matches: MatchResult[];
  testString: string;
  regexString: string;
  className?: string;
}

export function MatchStats({ matches, testString, regexString, className }: MatchStatsProps) {
  // 如果没有输入测试字符串，不显示任何信息
  if (!testString.trim()) {
    return null;
  }

  // 如果没有输入正则表达式，提示用户
  if (!regexString.trim()) {
    return (
      <div className={cn("text-sm text-slate-500 italic", className)}>
        请输入正则表达式以开始匹配
      </div>
    );
  }

  // 如果没有匹配结果，显示明确的无匹配提示
  if (matches.length === 0) {
    return (
      <div className={cn("text-sm space-y-2", className)}>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-orange-500"></div>
          <span className="text-orange-400 font-medium">无匹配结果</span>
        </div>
        <div className="text-slate-500 text-xs pl-4">
          正则表达式 <code className="bg-slate-700 px-1 rounded text-slate-300">{regexString}</code> 在测试字符串中未找到匹配项
        </div>
      </div>
    );
  }

  // 有匹配结果时显示统计信息
  const totalLength = matches.reduce((sum, match) => sum + match.text.length, 0);
  const averageLength = totalLength / matches.length;

  return (
    <div className={cn("text-sm text-slate-400 space-y-2", className)}>
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 rounded-full bg-green-500"></div>
        <span className="text-green-400 font-medium">找到 {matches.length} 个匹配</span>
      </div>
      <div className="grid grid-cols-3 gap-2 text-xs pl-4">
        <div className="flex flex-col">
          <span className="text-slate-500">匹配数量</span>
          <span className="text-green-400 font-semibold">{matches.length}</span>
        </div>
        <div className="flex flex-col">
          <span className="text-slate-500">总长度</span>
          <span className="text-blue-400">{totalLength}</span>
        </div>
        <div className="flex flex-col">
          <span className="text-slate-500">平均长度</span>
          <span className="text-purple-400">{averageLength.toFixed(1)}</span>
        </div>
      </div>
    </div>
  );
}

/**
 * 可编辑的高亮文本组件
 * 结合了输入功能和高亮显示
 */
interface EditableHighlightedTextProps {
  value: string;
  onChange: (value: string) => void;
  matches: MatchResult[];
  regexString?: string;
  placeholder?: string;
  className?: string;
  'aria-label'?: string;
}

export function EditableHighlightedText({
  value,
  onChange,
  matches,
  regexString = '',
  placeholder = "请输入测试字符串...",
  className,
  'aria-label': ariaLabel
}: EditableHighlightedTextProps) {
  const [isFocused, setIsFocused] = React.useState(false);

  // 计算匹配状态
  const getMatchStatus = () => {
    if (!value.trim()) return 'empty';
    if (!regexString.trim()) return 'no-regex';
    if (matches.length === 0) return 'no-match';
    return 'has-match';
  };

  const matchStatus = getMatchStatus();

  // 根据状态确定边框颜色
  const getBorderColor = () => {
    if (isFocused) return "border-blue-500 ring-1 ring-blue-500/20";

    switch (matchStatus) {
      case 'has-match':
        return "border-green-500/50";
      case 'no-match':
        return "border-orange-500/50";
      case 'no-regex':
        return "border-slate-600";
      case 'empty':
      default:
        return "border-slate-600";
    }
  };

  return (
    <div className={cn("relative", className)}>
      {/* 状态指示器 */}
      {value.trim() && regexString.trim() && (
        <div className="absolute top-2 right-2 z-10 pointer-events-none">
          <div className={cn(
            "flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium",
            matchStatus === 'has-match'
              ? "bg-green-500/20 text-green-400 border border-green-500/30"
              : "bg-orange-500/20 text-orange-400 border border-orange-500/30"
          )}>
            <div className={cn(
              "w-1.5 h-1.5 rounded-full",
              matchStatus === 'has-match' ? "bg-green-400" : "bg-orange-400"
            )}></div>
            <span>
              {matchStatus === 'has-match'
                ? `${matches.length} 个匹配`
                : "无匹配"
              }
            </span>
          </div>
        </div>
      )}

      {/* 隐藏的 textarea 用于输入 */}
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        aria-label={ariaLabel}
        className={cn(
          "absolute inset-0 w-full h-full resize-none bg-transparent text-transparent caret-white",
          "border-0 outline-none focus:ring-0 p-3 font-mono text-sm leading-relaxed",
          "placeholder:text-slate-500"
        )}
        style={{ caretColor: 'white' }}
      />

      {/* 高亮显示层 */}
      <div className={cn(
        "pointer-events-none p-3 min-h-[120px] bg-slate-900 border rounded-lg transition-colors",
        getBorderColor()
      )}>
        {value ? (
          <HighlightedText
            text={value}
            matches={matches}
            className="min-h-[96px]"
          />
        ) : (
          <div className="text-slate-500 font-mono text-sm leading-relaxed">
            {placeholder}
          </div>
        )}
      </div>
    </div>
  );
}
