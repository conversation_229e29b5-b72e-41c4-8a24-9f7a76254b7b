'use client';

import React, { useState } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from '../../lib/i18n';

interface LanguageSwitcherProps {
  className?: string;
  variant?: 'button' | 'dropdown' | 'toggle';
  size?: 'sm' | 'md' | 'lg';
}

/**
 * 语言切换组件
 */
export function LanguageSwitcher({ 
  className = '', 
  variant = 'dropdown',
  size = 'md' 
}: LanguageSwitcherProps) {
  const { 
    currentLocale, 
    changeLanguage, 
    toggleLanguage, 
    getAvailableLanguages 
  } = useLanguage();
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const availableLanguages = getAvailableLanguages();
  const currentLanguage = availableLanguages.find(lang => lang.code === currentLocale);

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-2',
    lg: 'text-base px-4 py-3',
  };

  // 切换按钮样式
  if (variant === 'toggle') {
    return (
      <button
        onClick={toggleLanguage}
        className={`
          inline-flex items-center space-x-1 rounded-md border border-gray-300 
          bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 
          focus:ring-blue-500 focus:ring-offset-2 transition-colors
          ${sizeClasses[size]} ${className}
        `}
        title={`Switch to ${currentLocale === 'zh-CN' ? 'English' : '中文'}`}
      >
        <span>{currentLanguage?.flag}</span>
        <span>{currentLanguage?.name}</span>
      </button>
    );
  }

  // 简单按钮样式
  if (variant === 'button') {
    return (
      <div className={`inline-flex rounded-md shadow-sm ${className}`}>
        {availableLanguages.map((language) => (
          <button
            key={language.code}
            onClick={() => changeLanguage(language.code)}
            className={`
              relative inline-flex items-center space-x-1 border border-gray-300 
              ${sizeClasses[size]} font-medium transition-colors
              ${currentLocale === language.code
                ? 'bg-blue-600 text-white border-blue-600 z-10'
                : 'bg-white text-gray-700 hover:bg-gray-50'
              }
              ${language.code === availableLanguages[0].code ? 'rounded-l-md' : ''}
              ${language.code === availableLanguages[availableLanguages.length - 1].code ? 'rounded-r-md' : ''}
              ${language.code !== availableLanguages[0].code ? '-ml-px' : ''}
            `}
          >
            <span>{language.flag}</span>
            <span>{language.name}</span>
          </button>
        ))}
      </div>
    );
  }

  // 下拉菜单样式（默认）
  return (
    <div className={`relative inline-block text-left ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          inline-flex items-center justify-between w-full rounded-md border 
          border-gray-300 bg-white shadow-sm hover:bg-gray-50 
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          transition-colors ${sizeClasses[size]}
        `}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <div className="flex items-center space-x-2">
          <span>{currentLanguage?.flag}</span>
          <span>{currentLanguage?.name}</span>
        </div>
        <svg
          className={`ml-2 h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* 下拉菜单 */}
          <div className="absolute right-0 z-20 mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            <div className="py-1">
              {availableLanguages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => {
                    changeLanguage(language.code);
                    setIsOpen(false);
                  }}
                  className={`
                    flex w-full items-center space-x-3 px-4 py-2 text-sm transition-colors
                    ${currentLocale === language.code
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                    }
                  `}
                >
                  <span className="text-lg">{language.flag}</span>
                  <span>{language.name}</span>
                  {currentLocale === language.code && (
                    <svg
                      className="ml-auto h-4 w-4 text-blue-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

/**
 * 紧凑的语言指示器
 */
export function LanguageIndicator({ className = '' }: { className?: string }) {
  const { currentLocale, getAvailableLanguages } = useLanguage();
  const currentLanguage = getAvailableLanguages().find(lang => lang.code === currentLocale);

  return (
    <div className={`inline-flex items-center space-x-1 ${className}`}>
      <span className="text-sm">{currentLanguage?.flag}</span>
      <span className="text-xs text-gray-500 uppercase">{currentLocale}</span>
    </div>
  );
}
