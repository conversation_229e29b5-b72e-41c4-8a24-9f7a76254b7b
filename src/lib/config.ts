/**
 * 应用配置管理
 * 统一管理所有环境变量和配置项
 */

/**
 * 防抖配置
 */
export const DEBOUNCE_CONFIG = {
  DELAY: parseInt(process.env.NEXT_PUBLIC_DEBOUNCE_DELAY || '300', 10),
} as const;

/**
 * AI 缓存配置
 */
export const AI_CACHE_CONFIG = {
  LIMIT: parseInt(process.env.NEXT_PUBLIC_AI_CACHE_LIMIT || '100', 10),
  TTL: parseInt(process.env.NEXT_PUBLIC_AI_CACHE_TTL || '3600000', 10), // 1 hour
} as const;

/**
 * 性能监控配置
 */
export const PERFORMANCE_CONFIG = {
  MAX_HISTORY_SIZE: parseInt(process.env.NEXT_PUBLIC_PERF_HISTORY_SIZE || '100', 10),
  SNAPSHOT_LIMIT: parseInt(process.env.NEXT_PUBLIC_SNAPSHOT_LIMIT || '50', 10),
  WARNING_THRESHOLDS: {
    PARSE_TIME: parseInt(process.env.NEXT_PUBLIC_PARSE_WARNING_MS || '100', 10),
    LAYOUT_TIME: parseInt(process.env.NEXT_PUBLIC_LAYOUT_WARNING_MS || '500', 10),
    TOTAL_TIME: parseInt(process.env.NEXT_PUBLIC_TOTAL_WARNING_MS || '1000', 10),
    NODE_COUNT: parseInt(process.env.NEXT_PUBLIC_NODE_WARNING_COUNT || '50', 10),
    COMPLEXITY: parseInt(process.env.NEXT_PUBLIC_COMPLEXITY_WARNING || '200', 10),
    MEMORY_USAGE: parseInt(process.env.NEXT_PUBLIC_MEMORY_WARNING_MB || '100', 10) * 1024 * 1024,
  },
} as const;

/**
 * API 配置
 */
export const API_CONFIG = {
  TIMEOUT_MS: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT_MS || '30000', 10),
  MAX_RETRIES: parseInt(process.env.NEXT_PUBLIC_API_MAX_RETRIES || '3', 10),
  RATE_LIMIT_PER_MINUTE: parseInt(process.env.NEXT_PUBLIC_RATE_LIMIT_PER_MINUTE || '60', 10),
  
  // LLM 相关配置
  OPENAI_API_KEY: process.env.OPENAI_API_KEY,
  ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,
  OPENAI_MODEL: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
  ANTHROPIC_MODEL: process.env.ANTHROPIC_MODEL || 'claude-3-haiku-20240307',
  
  // CORS 配置
  CORS_ORIGINS: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
} as const;

/**
 * 测试配置
 */
export const TEST_CONFIG = {
  TIMEOUT_MS: parseInt(process.env.TEST_TIMEOUT_MS || '10000', 10),
  PERFORMANCE_TOLERANCE: parseFloat(process.env.TEST_PERFORMANCE_TOLERANCE || '0.2'), // 20% tolerance
  MEMORY_TOLERANCE_MB: parseInt(process.env.TEST_MEMORY_TOLERANCE_MB || '50', 10),
} as const;

/**
 * 日志配置
 */
export const LOG_CONFIG = {
  LEVEL: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'development' ? 'DEBUG' : 'WARN'),
  MAX_MESSAGE_LENGTH: parseInt(process.env.LOG_MAX_MESSAGE_LENGTH || '1000', 10),
} as const;

/**
 * 开发环境检测
 */
export const ENV = {
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  IS_TEST: process.env.NODE_ENV === 'test',
} as const;

/**
 * 验证必需的环境变量
 */
export function validateEnvironment(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 在生产环境中检查必需的 API 密钥
  if (ENV.IS_PRODUCTION) {
    if (!API_CONFIG.OPENAI_API_KEY && !API_CONFIG.ANTHROPIC_API_KEY) {
      errors.push('At least one of OPENAI_API_KEY or ANTHROPIC_API_KEY must be set in production');
    }
  }
  
  // 验证数值配置的合理性
  if (AI_CACHE_CONFIG.LIMIT <= 0) {
    errors.push('AI_CACHE_LIMIT must be greater than 0');
  }
  
  if (DEBOUNCE_CONFIG.DELAY < 0) {
    errors.push('DEBOUNCE_DELAY must be non-negative');
  }
  
  if (API_CONFIG.TIMEOUT_MS <= 0) {
    errors.push('API_TIMEOUT_MS must be greater than 0');
  }
  
  if (API_CONFIG.MAX_RETRIES < 0) {
    errors.push('API_MAX_RETRIES must be non-negative');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * 获取配置摘要（用于调试）
 */
export function getConfigSummary(): Record<string, any> {
  return {
    environment: process.env.NODE_ENV,
    debounce: {
      delay: DEBOUNCE_CONFIG.DELAY,
    },
    cache: {
      limit: AI_CACHE_CONFIG.LIMIT,
      ttl: AI_CACHE_CONFIG.TTL,
    },
    performance: {
      historySize: PERFORMANCE_CONFIG.MAX_HISTORY_SIZE,
      snapshotLimit: PERFORMANCE_CONFIG.SNAPSHOT_LIMIT,
    },
    api: {
      timeout: API_CONFIG.TIMEOUT_MS,
      maxRetries: API_CONFIG.MAX_RETRIES,
      rateLimit: API_CONFIG.RATE_LIMIT_PER_MINUTE,
      hasOpenAIKey: !!API_CONFIG.OPENAI_API_KEY,
      hasAnthropicKey: !!API_CONFIG.ANTHROPIC_API_KEY,
    },
    test: {
      timeout: TEST_CONFIG.TIMEOUT_MS,
      performanceTolerance: TEST_CONFIG.PERFORMANCE_TOLERANCE,
    },
    log: {
      level: LOG_CONFIG.LEVEL,
      maxMessageLength: LOG_CONFIG.MAX_MESSAGE_LENGTH,
    },
  };
}
