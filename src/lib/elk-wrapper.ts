"use client"

// 客户端专用的 ELK 包装器，避免 SSR 问题

let elkInstance: any = null;
let elkPromise: Promise<any> | null = null;

/**
 * 动态加载 ELK 实例
 */
async function getElkInstance() {
  if (elkInstance) {
    return elkInstance;
  }

  if (!elkPromise) {
    elkPromise = (async () => {
      try {
        // 只在浏览器环境中加载 ELK
        if (typeof window === 'undefined') {
          return createMockLayouter();
        }

        // 使用正确的 elkjs 主入口点进行动态导入
        const elkModule = await import('elkjs');

        // elkjs 主模块导出 ELK 构造函数
        const ELKConstructor = elkModule.default || elkModule;

        if (!ELKConstructor) {
          throw new Error('ELK 构造函数未找到，请确认 elkjs 版本和导出结构');
        }

        elkInstance = new ELKConstructor();
        return elkInstance;
      } catch (error) {
        console.error('Failed to load ELK module:', error);
        console.warn('Falling back to mock layouter due to ELK import failure');
        // 如果 ELK 加载失败，返回一个模拟的布局器
        return createMockLayouter();
      }
    })();
  }

  return elkPromise;
}

/**
 * 创建模拟的布局器（当 ELK 无法加载时使用）
 */
function createMockLayouter() {
  return {
    layout: async (graph: any) => {
      console.warn('Using mock layouter - ELK failed to load');

      // 网格布局，避免溢出视口
      const layoutedGraph = { ...graph };

      if (layoutedGraph.children) {
        // 限制每行最大节点数，避免大量节点时视口溢出
        const MAX_PER_ROW = 20;
        const idealNodesPerRow = Math.ceil(Math.sqrt(layoutedGraph.children.length));
        const nodesPerRow = Math.min(idealNodesPerRow, MAX_PER_ROW);
        const nodeWidth = 120;
        const nodeHeight = 60;
        const spacing = 20;

        layoutedGraph.children = layoutedGraph.children.map((child: any, index: number) => {
          const row = Math.floor(index / nodesPerRow);
          const col = index % nodesPerRow;

          return {
            ...child,
            x: col * (nodeWidth + spacing) + 50,
            y: row * (nodeHeight + spacing) + 50,
            width: child.width || nodeWidth,
            height: child.height || nodeHeight
          };
        });

        // 简单处理边的位置，避免重叠
        if (layoutedGraph.edges) {
          layoutedGraph.edges = layoutedGraph.edges.map((edge: any) => {
            const sourceNode = layoutedGraph.children?.find((n: any) => n.id === edge.source);
            const targetNode = layoutedGraph.children?.find((n: any) => n.id === edge.target);

            if (sourceNode && targetNode) {
              return {
                ...edge,
                sections: [{
                  startPoint: {
                    x: sourceNode.x + (sourceNode.width || nodeWidth) / 2,
                    y: sourceNode.y + (sourceNode.height || nodeHeight) / 2
                  },
                  endPoint: {
                    x: targetNode.x + (targetNode.width || nodeWidth) / 2,
                    y: targetNode.y + (targetNode.height || nodeHeight) / 2
                  }
                }]
              };
            }
            return edge;
          });
        }
      }

      return layoutedGraph;
    }
  };
}

/**
 * 客户端安全的布局函数
 */
export async function performLayout(graph: any): Promise<any> {
  try {
    const elk = await getElkInstance();
    return await elk.layout(graph);
  } catch (error) {
    console.error('Layout failed:', error);

    // 降级到网格布局
    const fallbackGraph = { ...graph };
    if (fallbackGraph.children) {
      // 限制每行最大节点数，避免大量节点时视口溢出
      const MAX_PER_ROW = 20;
      const idealNodesPerRow = Math.ceil(Math.sqrt(fallbackGraph.children.length));
      const nodesPerRow = Math.min(idealNodesPerRow, MAX_PER_ROW);
      const nodeWidth = 120;
      const nodeHeight = 60;
      const spacing = 20;

      fallbackGraph.children = fallbackGraph.children.map((child: any, index: number) => {
        const row = Math.floor(index / nodesPerRow);
        const col = index % nodesPerRow;

        return {
          ...child,
          x: col * (nodeWidth + spacing) + 50,
          y: row * (nodeHeight + spacing) + 50,
          width: child.width || nodeWidth,
          height: child.height || nodeHeight
        };
      });

      // 简单处理边的位置，避免重叠
      if (fallbackGraph.edges) {
        fallbackGraph.edges = fallbackGraph.edges.map((edge: any) => {
          const sourceNode = fallbackGraph.children?.find((n: any) => n.id === edge.source);
          const targetNode = fallbackGraph.children?.find((n: any) => n.id === edge.target);

          if (sourceNode && targetNode) {
            return {
              ...edge,
              sections: [{
                startPoint: {
                  x: sourceNode.x + (sourceNode.width || nodeWidth) / 2,
                  y: sourceNode.y + (sourceNode.height || nodeHeight) / 2
                },
                endPoint: {
                  x: targetNode.x + (targetNode.width || nodeWidth) / 2,
                  y: targetNode.y + (targetNode.height || nodeHeight) / 2
                }
              }]
            };
          }
          return edge;
        });
      }
    }

    return fallbackGraph;
  }
}

/**
 * 检查 ELK 是否可用
 */
export async function isElkAvailable(): Promise<boolean> {
  try {
    await getElkInstance();
    return elkInstance !== null;
  } catch {
    return false;
  }
}

/**
 * 重置 ELK 实例（用于测试或错误恢复）
 *
 * 清除缓存的 ELK 实例和 Promise，强制下次调用时重新创建。
 * 主要用于：
 * - 单元测试中的清理工作
 * - ELK 加载失败后的错误恢复
 * - 强制重新初始化布局引擎
 */
export function resetElkInstance(): void {
  elkInstance = null;
  elkPromise = null;
}
