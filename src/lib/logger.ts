// lib/logger.ts
import adze, { setup } from 'adze';

// 创建一个全局 log store，用于监听、传输日志
const store = setup({
  activeLevel: process.env.LOG_LEVEL?.toLowerCase() || 'debug', // 临时改为 debug 级别
  format: process.env.NODE_ENV === 'development' ? 'pretty' : 'json',
  withEmoji: process.env.NODE_ENV === 'development',
  showTimestamp: true,
  cache: true,
  cacheSize: 500,
  silent: process.env.NODE_ENV === 'test',
  meta: {
    app: 'RegexAI',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  },
});

store.addListener('*', (log) => {
  // 在服务端环境下，将日志输出到终端
  if (typeof window === 'undefined') {
    const timestamp = new Date().toISOString();
    // 安全地处理 level 字段，确保它是字符串类型
    const levelStr = typeof log.data.level === 'string'
      ? log.data.level.toUpperCase()
      : (log.data.levelName || String(log.data.level || 'UNKNOWN')).toUpperCase();
    // 日志输出已禁用
  }
});

// 创建一个带 emoji、namespace 和时间戳的 logger 工厂
const logger = adze
  .timestamp
  .ns('RegexAI') // 使用项目名作为命名空间
  .seal();

// 创建常用的 sealTag 模板，用于重复记录
const ERR = adze.timestamp.ns('RegexAI').withEmoji.sealTag('error');
const WARN = adze.timestamp.ns('RegexAI').withEmoji.sealTag('warn');
const INFO = adze.timestamp.ns('RegexAI').withEmoji.sealTag('info');
const SUCCESS = adze.timestamp.ns('RegexAI').withEmoji.sealTag('success');

export { logger, store, ERR, WARN, INFO, SUCCESS };


