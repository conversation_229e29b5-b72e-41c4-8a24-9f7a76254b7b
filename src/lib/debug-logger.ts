/**
 * 统一的调试日志配置
 * 用于排查连接线和节点之间没有正确连接的问题
 * 所有日志都输出到服务端终端
 */

// 检查是否在服务端环境
const isServer = typeof window === 'undefined';

// 简化的日志工具 - 只在开发环境输出到控制台
const log = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
        // 调试日志已禁用
    }
  },
  success: (message: string) => {
    if (process.env.NODE_ENV === 'development') {
      // 成功日志已禁用
    }
  },
  error: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.error(`\n🚨 ${message}`);
      if (data) {
        console.error(JSON.stringify(data, null, 2));
      }
    }
  },
  warn: (message: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`\n⚠️ ${message}`);
    }
  }
};

/**
 * BUG排查日志 - 专门用于排查连接问题
 */
export const bugLogger = {
  /**
   * 1. ELK布局后的边信息检查
   */
  elkEdgesInfo: (data: {
    hasEdges: boolean;
    edgesCount: number;
    edgesDetail: Array<{
      id: string;
      sources: string[];
      targets: string[];
      sourcesLength: number;
      targetsLength: number;
    }>;
  }) => {
    log.info('🔍 [BUG排查-1] ELK布局后的边信息', data);

    // 量化指标检查
    if (data.edgesCount === 0) {
      log.error('🚨 [BUG排查-1] 致命错误: 没有生成任何边');
    } else {
      log.success(`✅ [BUG排查-1] 边生成正常: ${data.edgesCount}条边`);
    }
  },

  /**
   * 2. 节点位置赋值检查
   */
  nodePositionCheck: (data: {
    nodeId: string;
    elkX?: number;
    elkY?: number;
    reactFlowPosition: { x: number; y: number };
  }) => {
    if (data.elkX === undefined || data.elkY === undefined) {
      log.error('🚨 [BUG排查-2] 节点位置未定义', data);
    } else if (data.reactFlowPosition.x !== data.elkX || data.reactFlowPosition.y !== data.elkY) {
      log.error('🚨 [BUG排查-2] 节点位置赋值不匹配', data);
    } else {
      log.success(`✅ [BUG排查-2] 节点位置赋值正确: ${data.nodeId}`);
    }
  },

  /**
   * 3. 节点ID唯一性检查
   */
  nodeIdsUniqueness: (data: {
    nodeCount: number;
    nodeIds: string[];
    hasDuplicateIds: boolean;
  }) => {
    log.info('🔍 [BUG排查-3] 所有节点ID汇总', data);

    if (data.hasDuplicateIds) {
      log.error('🚨 [BUG排查-3] 致命错误: 存在重复的节点ID');
    } else {
      log.success(`✅ [BUG排查-3] 节点ID唯一性正常: ${data.nodeCount}个节点`);
    }
  },

  /**
   * 4. 边的source/target匹配检查
   */
  edgeSourceTargetMatch: (data: {
    edgeId: string;
    source: string;
    target: string;
    sourceExists: boolean;
    targetExists: boolean;
    availableNodeIds?: string[];
  }) => {
    if (!data.sourceExists || !data.targetExists) {
      log.error('🚨 [BUG排查-4] 边的source/target节点不存在', data);
    } else {
      log.success(`✅ [BUG排查-4] 边的source/target节点匹配正确: ${data.edgeId}`);
    }
  },

  /**
   * 5. 最终数据完整性检查
   */
  finalDataIntegrity: (data: {
    totalNodes: number;
    totalEdges: number;
    orphanEdgesCount: number;
    orphanEdges: Array<{
      id: string;
      source: string;
      target: string;
      sourceExists: boolean;
      targetExists: boolean;
    }>;
    nodePositionSummary: Array<{
      id: string;
      position: { x: number; y: number };
      hasValidPosition: boolean;
    }>;
  }) => {
    log.info('🔍 [BUG排查-5] 最终数据完整性检查', data);

    // 量化指标检查
    if (data.orphanEdgesCount > 0) {
      log.error(`🚨 [BUG排查-5] 致命错误: 存在${data.orphanEdgesCount}条孤立边`);
    } else {
      log.success('✅ [BUG排查-5] 数据完整性正常: 无孤立边');
    }

    const invalidPositionNodes = data.nodePositionSummary.filter(n => !n.hasValidPosition);
    if (invalidPositionNodes.length > 0) {
      log.error(`🚨 [BUG排查-5] 致命错误: ${invalidPositionNodes.length}个节点位置无效`);
    } else {
      log.success('✅ [BUG排查-5] 节点位置有效性正常');
    }
  },

  /**
   * 6. 复杂AST结构分析
   */
  complexAstAnalysis: (data: {
    astType: string;
    bodyType?: string;
    bodyChildren: number;
    bodyExpressions: number;
    bodyAlternatives: number;
    preprocessedCount: number;
    isMultiNode: boolean;
  }) => {
    log.info('🔍 [BUG排查-COMPLEX] 复杂AST结构分析', data);

    // 量化指标检查
    if (data.bodyChildren === 0 && data.bodyExpressions === 0 && data.bodyAlternatives === 0) {
      log.error('🚨 [BUG排查-COMPLEX] 致命错误: AST结构解析异常，所有子节点数量为0');
    } else {
      log.success('✅ [BUG排查-COMPLEX] AST结构解析正常');
    }
  },

  /**
   * 7. 预处理函数调用跟踪
   */
  preprocessFunctionCall: (data: {
    inputNodeType: string;
    inputNodeExpressions: number;
    callCount: number;
  }) => {
    log.info(`🔍 [BUG排查-PREPROCESS] 预处理函数调用 #${data.callCount}`, data);

    if (data.callCount > 1) {
      log.warn(`⚠️ [BUG排查-PREPROCESS] 警告: 预处理函数被重复调用${data.callCount}次`);
    }
  },

  /**
   * 8. 节点遍历详细跟踪
   */
  traverseNodeDetail: (data: {
    inputNodeType: string;
    outputNodeCount: number;
    outputEdgeCount: number;
    parentId: string;
    depth: number;
    outputNodes: Array<{
      id: string;
      dataContent: string;
      dataSemanticType: string;
    }>;
    outputEdges: Array<{
      id: string;
      sources: string[];
      targets: string[];
      hasValidSources: boolean;
      hasValidTargets: boolean;
    }>;
  }) => {
    log.info(`🔍 [BUG排查-TRAVERSE] 节点遍历: ${data.inputNodeType}`, data);

    // 检查边的有效性
    const invalidEdges = data.outputEdges.filter(e => !e.hasValidSources || !e.hasValidTargets);
    if (invalidEdges.length > 0) {
      log.error(`🚨 [BUG排查-TRAVERSE] 发现${invalidEdges.length}条无效边`);
    }
  }
};

/**
 * 量化测试指标汇总
 */
export const testMetrics = {
  /**
   * 生成测试报告
   */
  generateReport: (regex: string, data: {
    bodyChildren: number;
    bodyExpressions: number;
    bodyAlternatives: number;
    totalNodes: number;
    totalEdges: number;
    edgesCount: number;
    orphanEdgesCount: number;
    preprocessCallCount: number;
  }) => {
    log.info(`📊 [测试报告] 正则表达式: ${regex}`);

    const report = {
      '输入': regex,
      '检查结果': {
        '1. bodyChildren': `${data.bodyChildren} (期望:>0)`,
        '2. bodyExpressions': `${data.bodyExpressions} (期望:>0)`,
        '3. bodyAlternatives': `${data.bodyAlternatives} (期望:>0)`,
        '4. totalNodes': `${data.totalNodes} (期望:≥4)`,
        '5. edgesCount': `${data.edgesCount} (期望:>0)`,
        '6. orphanEdgesCount': `${data.orphanEdgesCount} (期望:0)`,
        '7. preprocessCallCount': `${data.preprocessCallCount} (期望:1)`
      },
      '问题诊断': []
    };

    // 自动诊断问题
    const issues = [];
    if (data.bodyChildren === 0 && data.bodyExpressions === 0 && data.bodyAlternatives === 0) {
      issues.push('AST解析阶段有问题');
    }
    if (data.totalNodes < 4) {
      issues.push('节点生成阶段有问题');
    }
    if (data.edgesCount === 0) {
      issues.push('边生成阶段有问题');
    }
    if (data.orphanEdgesCount > 0) {
      issues.push('最终连接阶段有问题');
    }
    if (data.preprocessCallCount > 1) {
      issues.push('预处理函数重复调用问题');
    }

    (report as any)['问题诊断'] = issues.length > 0 ? issues : ['无明显问题'];

    log.info('📊 [测试报告] 详细结果', report);
    
    return report;
  }
};
