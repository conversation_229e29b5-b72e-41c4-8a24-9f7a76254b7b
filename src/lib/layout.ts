import ELK from 'elkjs';
import type { Node, Edge } from '@xyflow/react';
import type { AstRegExp } from 'regexp-tree/ast';
import type { ElkNode, ElkExtendedEdge } from 'elkjs';
import { astToElkGraph } from './transformer';
import type { CustomElkNode, NodeData } from '@/types/graph';
import { NodeType } from '@/types/graph';
import { LAYOUT_CONSTANTS, type LayoutConfig, DEFAULT_LAYOUT_CONFIG } from './constants';
import { logger } from './logger';

/**
 * 节点处理错误类（非关键错误）
 */
export class NodeProcessingError extends Error {
  constructor(message: string, public readonly nodeId?: string, public readonly cause?: Error) {
    super(message)
    this.name = 'NodeProcessingError'
  }
}

/**
 * ELK 边接口定义 - 与 transformer.ts 中的 ElkExtendedEdge 保持一致
 */
interface ElkEdge extends ElkExtendedEdge {
  id: string;
  sources: string[];
  targets: string[];
}

/**
 * 递归 ELK 图结构类型
 */
interface RecursiveElkGraph extends ElkNode {
  children?: RecursiveElkGraph[];
  edges?: ElkExtendedEdge[];
}

/**
 * 布局结果类型
 */
interface LayoutResult {
  nodes: Node<NodeData>[];
  edges: Edge[];
}

/**
 * 布局缓存
 */
const layoutCache = new WeakMap<AstRegExp, LayoutResult>();

/**
 * 清除布局缓存（用于内存管理）
 */
export function clearLayoutCache(): void {
  // WeakMap 会自动垃圾回收，这里主要用于显式清理

}

/**
 * 根据 AST 节点类型获取边的样式（未来扩展）
 * 目前暂未使用，保留作为未来功能的参考
 */
/*
function getEdgeStyleForAstType(astNodeType: string): Partial<Edge> {
  // 未来可以根据不同的 AST 节点类型返回不同的边样式
  switch (astNodeType) {
    case 'Quantifier':
    case 'Repetition':
      return {
        animated: true,
        style: { strokeDasharray: '5,5' }
      };
    case 'Disjunction':
      return {
        markerEnd: {
          type: 'arrowclosed' as MarkerType,
          width: 20,
          height: 20
        }
      };
    default:
      return {};
  }
}
*/

/**
 * 布局计算错误类
 */
export class LayoutError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'LayoutError';
  }
}

/**
 * 根据语义类型决定 React Flow 节点类型
 */
function getReactFlowNodeType(semanticType: NodeType): string {
  switch (semanticType) {
    case NodeType.MACRO_SEQUENCE:
      return 'macro_sequence';
    default:
      return 'regexNode';
  }
}

/**
 * 将 ELK 节点转换为 React Flow 节点
 */
function elkNodeToFlowNode(elkNode: CustomElkNode): Node<NodeData> {
  const nodeData: NodeData = elkNode.data || {
    semanticType: 'literal' as any,
    content: 'Unknown',
    originalText: 'Unknown',
    astNodeType: 'Unknown'
  };

  // 根据语义类型决定 React Flow 节点类型
  const reactFlowNodeType = getReactFlowNodeType(nodeData.semanticType);

  return {
    id: elkNode.id,
    type: reactFlowNodeType, // 使用动态决定的节点类型
    position: {
      x: elkNode.x || 0,
      y: elkNode.y || 0
    },
    data: {
      label: nodeData.content,
      ...nodeData
    },
    style: {
      width: elkNode.width || 60,
      height: elkNode.height || 40
    }
  };
}

/**
 * 将 ELK 边转换为 React Flow 边（支持多源/多目标）
 */
function elkEdgeToFlowEdges(elkEdge: ElkEdge): Edge[] {
  // 边缘情况处理：检查必要的属性
  if (!elkEdge.id || !elkEdge.sources || !elkEdge.targets) {
    throw new LayoutError('无效的边对象：缺少必要属性', 'INVALID_EDGE');
  }

  if (elkEdge.sources.length === 0 || elkEdge.targets.length === 0) {
    throw new LayoutError('无效的边对象：源节点或目标节点为空', 'EMPTY_EDGE_ENDPOINTS');
  }

  const edges: Edge[] = [];

  // 处理多源/多目标情况，生成所有可能的边组合
  for (let i = 0; i < elkEdge.sources.length; i++) {
    for (let j = 0; j < elkEdge.targets.length; j++) {
      const edgeId = elkEdge.sources.length === 1 && elkEdge.targets.length === 1
        ? elkEdge.id
        : `${elkEdge.id}_${i}_${j}`;

      const reactFlowEdge = {
        id: edgeId,
        source: elkEdge.sources[i],
        target: elkEdge.targets[j],
        type: 'default', // 可以根据需要自定义边类型
        animated: false,
        // 未来可以根据 AST 类型动态配置
        // markerEnd: getMarkerForEdgeType(edgeType),
        // style: getStyleForEdgeType(edgeType)
      };


      edges.push(reactFlowEdge);
    }
  }


  return edges;
}

/**
 * 递归计算图中的节点总数
 */
function countNodesRecursively(graph: RecursiveElkGraph): number {
  let count = 0;

  if (graph.children) {
    count += graph.children.length;
    for (const child of graph.children) {
      count += countNodesRecursively(child);
    }
  }

  return count;
}

/**
 * 创建空图占位符
 */
function createEmptyPlaceholder(): LayoutResult {
  const placeholderData: NodeData = {
    semanticType: 'literal' as any,
    content: '空正则表达式',
    originalText: '',
    astNodeType: 'Empty'
  };

  return {
    nodes: [{
      id: 'empty-placeholder',
      type: 'regexNode', // 使用正确的节点类型
      position: { x: 100, y: 100 },
      data: {
        label: '空正则表达式',
        ...placeholderData
      },
      style: {
        width: 120,
        height: 40
      },
      className: 'empty-placeholder-node' // 使用CSS类处理主题样式
    }],
    edges: []
  };
}

/**
 * 根据图复杂度优化布局配置
 */
function getOptimizedLayoutOptions(nodeCount: number, _edgeCount: number, _config: LayoutConfig = {}): Record<string, any> {
  const maxSpacing = LAYOUT_CONSTANTS.MAX_SPACING;

  // 基础间距，根据节点数量调整
  let nodeSpacing = Math.max(50, Math.min(100 - nodeCount * 0.5, maxSpacing.NODE_NODE));
  let layerSpacing = Math.max(60, Math.min(120 - nodeCount * 0.3, maxSpacing.LAYER_SPACING));

  // 对于复杂图形，使用更紧凑的布局
  if (nodeCount > 50) {
    nodeSpacing = Math.min(nodeSpacing, 60);
    layerSpacing = Math.min(layerSpacing, 80);
  }

  // 未来可以根据 edgeCount 和 config 进一步优化布局
  // 目前先用下划线前缀标记未使用的参数

  return {
    'elk.algorithm': 'layered',
    'elk.direction': 'RIGHT',
    'elk.spacing.nodeNode': nodeSpacing,
    'elk.layered.spacing.nodeNodeBetweenLayers': layerSpacing,
    'elk.layered.crossingMinimization.strategy': 'LAYER_SWEEP',
    'elk.layered.nodePlacement.strategy': 'SIMPLE'
  };
}

/**
 * 使用显式栈遍历 ELK 图结构，提取所有节点和边
 */
function extractNodesAndEdges(elkGraph: RecursiveElkGraph, config: LayoutConfig = {}): LayoutResult {
  const maxNodes = config.maxNodes || LAYOUT_CONSTANTS.MAX_NODES;
  const nodes: Node<NodeData>[] = [];
  const edges: Edge[] = [];

  // 使用显式栈避免递归深度问题
  const stack: { graph: RecursiveElkGraph; depth: number }[] = [{ graph: elkGraph, depth: 0 }];
  const maxDepth = config.maxRecursionDepth || LAYOUT_CONSTANTS.MAX_RECURSION_DEPTH;

  while (stack.length > 0) {
    const { graph, depth } = stack.pop()!;

    // 检查递归深度
    if (depth > maxDepth) {
      logger.warn(`达到最大递归深度 ${maxDepth}，跳过更深层级的节点`);
      continue;
    }

    // 处理当前层级的子节点
    if (graph.children && Array.isArray(graph.children)) {
      for (const child of graph.children) {
        // 性能检查：如果节点数量过多，直接抛出错误
        if (nodes.length >= maxNodes) {
          throw new LayoutError(
            `图过大，节点数量超过限制。当前节点数: ${nodes.length}，限制: ${maxNodes}`,
            'GRAPH_TOO_LARGE'
          );
        }

        try {
          // 转换节点
          const flowNode = elkNodeToFlowNode(child);
          nodes.push(flowNode);

          // 将子节点加入栈中待处理
          if (child.children && child.children.length > 0) {
            stack.push({ graph: child, depth: depth + 1 });
          }
        } catch (error) {
          // 优化：区分不同类型的错误
          const nodeError = error instanceof Error ? error : new Error(String(error));

          if (error instanceof LayoutError) {
            // 严重的布局错误应该中断处理
            throw error;
          }

          // 创建节点处理错误（非关键错误）
          const processingError = new NodeProcessingError(
            `处理节点 ${child.id} 时出错: ${nodeError.message}`,
            child.id,
            nodeError
          );

          logger.error('处理节点时出错:', processingError);
          // 继续处理其他节点，不中断整个流程
        }
      }
    }

    // 处理边

    if (graph.edges && Array.isArray(graph.edges)) {
      for (const edge of graph.edges) {
        try {
          const flowEdges = elkEdgeToFlowEdges(edge);
          edges.push(...flowEdges);
        } catch (error) {
          // 优化：区分不同类型的错误
          const edgeError = error instanceof Error ? error : new Error(String(error));

          if (error instanceof LayoutError) {
            // 严重的布局错误应该中断处理
            throw error;
          }

          // 边处理错误通常不是关键错误，记录并继续
          logger.error(`处理边 ${edge.id} 时出错:`, edgeError);
          // 继续处理其他边，不中断整个流程
        }
      }
    }
  }

  // 性能监控日志
  const warningThreshold = maxNodes * LAYOUT_CONSTANTS.PERFORMANCE_WARNING_THRESHOLD;
  if (nodes.length > warningThreshold) {
    logger.warn(`节点数量较多(${nodes.length})，可能影响性能`);
  }

  return { nodes, edges };
}

/**
 * 获取经过布局计算的元素
 *
 * @param ast - regexp-tree 生成的 AST 对象
 * @param config - 布局配置选项
 * @returns 包含 nodes 和 edges 的对象，格式为 React Flow 所需
 */
export async function getLayoutedElements(
  ast: AstRegExp,
  config: LayoutConfig = {}
): Promise<LayoutResult> {
  try {

    // 验证输入
    if (!ast) {
      throw new LayoutError('AST 对象不能为空', 'INVALID_AST');
    }

    // 合并配置
    const finalConfig = { ...DEFAULT_LAYOUT_CONFIG, ...config };



    // 检查缓存
    if (finalConfig.enableCache && layoutCache.has(ast)) {

      return layoutCache.get(ast)!;
    }

    // a. 实例化 ELK
    const elk = new ELK();

    // b. 调用 astToElkGraph(ast) 得到 ELK 图结构
    const graph = astToElkGraph(ast);

    // 空图处理增强：检查是否为空图
    if (!graph.children || graph.children.length === 0) {

      return { nodes: [], edges: [] };
    }

    // 准确计算节点总数
    const totalNodeCount = countNodesRecursively(graph);
    const estimatedEdgeCount = graph.edges?.length || 0;

    // 检查图是否过大
    if (totalNodeCount > finalConfig.maxNodes) {
      logger.warn(`图过大，节点数: ${totalNodeCount}，限制: ${finalConfig.maxNodes}`);
      // 可以选择直接抛错或继续处理（会在 extractNodesAndEdges 中截断）
    }

    // 布局配置优化：根据图复杂度调整布局参数
    const optimizedLayoutOptions = getOptimizedLayoutOptions(
      totalNodeCount,
      estimatedEdgeCount,
      finalConfig
    );

    // 将优化的布局选项应用到图结构
    const optimizedGraph = {
      ...graph,
      layoutOptions: {
        ...graph.layoutOptions,
        ...optimizedLayoutOptions
      }
    };

    // c. 调用 await elk.layout(graph) 来进行布局计算
    const layoutedGraph = await elk.layout(optimizedGraph);

    // d. 遍历布局计算后的图，将其中的节点和边最终转换为 React Flow 需要的格式
    const result = extractNodesAndEdges(layoutedGraph, finalConfig);

    // 空图处理增强：如果处理后仍然没有节点，返回空状态
    if (result.nodes.length === 0) {

      return { nodes: [], edges: [] };
    }

    // 缓存结果
    if (finalConfig.enableCache) {
      layoutCache.set(ast, result);
    }



    // e. 返回包含 nodes 和 edges 的对象
    return result;

  } catch (error) {
    // 错误处理
    if (error instanceof LayoutError) {
      throw error; // 重新抛出我们自己的错误
    }

    if (error instanceof Error) {
      throw new LayoutError(
        `布局计算失败: ${error.message}`,
        'LAYOUT_CALCULATION_FAILED'
      );
    }

    throw new LayoutError(
      `布局计算失败: ${String(error)}`,
      'UNKNOWN_LAYOUT_ERROR'
    );
  }
}
