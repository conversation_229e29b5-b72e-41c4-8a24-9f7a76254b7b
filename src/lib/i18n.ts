/**
 * 国际化支持模块
 */

export type Locale = 'zh-CN' | 'en-US';

export interface TranslationMessages {
  errors: {
    parseError: string;
    layoutError: string;
    networkError: string;
    unknownError: string;
    regexParseFailure: string;
    layoutCalculationFailed: string;
    outOfMemory: string;
    maxCallStack: string;
    scriptError: string;
    elkLoadFailed: string;
    transformError: string;
    maxDepthExceeded: string;
    aiExplanationFailed: string;
  };
  suggestions: {
    checkSyntax: string;
    checkBrackets: string;
    checkInvalidChars: string;
    simplifyRegex: string;
    simplifyStructure: string;
    checkNesting: string;
    checkNetwork: string;
    retryLater: string;
    refreshPage: string;
    contactSupport: string;
  };
  placeholders: {
    emptyTitle: string;
    emptyDescription: string;
    errorTitle: string;
    inputPrompt: string;
  };
  performance: {
    parseTime: string;
    layoutTime: string;
    totalTime: string;
    nodeCount: string;
    edgeCount: string;
    complexity: string;
  };
  debug: {
    hoverTestPanel: string;
    currentHoveredElementId: string;
    aiExplanationCacheSize: string;
    cacheHitRate: string;
    currentHoveredElementExplanation: string;
    none: string;
    loading: string;
    notAvailable: string;
  };
  explanation: {
    title: string;
    hoverToSeeExplanation: string;
    loadingExplanation: string;
    aiExplanation: string;
    noExplanationAvailable: string;
    currentElement: string;
    retry: string;
  };
}

const translations: Record<Locale, TranslationMessages> = {
  'zh-CN': {
    errors: {
      parseError: '正则表达式解析错误',
      layoutError: '布局计算错误',
      networkError: '网络连接错误',
      unknownError: '未知错误',
      regexParseFailure: '正则表达式解析失败',
      layoutCalculationFailed: '布局计算失败',
      outOfMemory: '内存不足',
      maxCallStack: '调用栈溢出',
      scriptError: '脚本错误',
      elkLoadFailed: 'ELK 布局引擎加载失败',
      transformError: 'AST 转换错误',
      maxDepthExceeded: '达到最大递归深度，可能存在循环引用或表达式过于复杂',
      aiExplanationFailed: 'AI 解释获取失败',
    },
    suggestions: {
      checkSyntax: '检查正则表达式语法是否正确',
      checkBrackets: '检查是否有未闭合的括号或方括号',
      checkInvalidChars: '检查是否使用了无效的正则表达式字符',
      simplifyRegex: '尝试使用更简单的正则表达式',
      simplifyStructure: '尝试简化正则表达式结构',
      checkNesting: '检查是否有过深的嵌套',
      checkNetwork: '检查网络连接',
      retryLater: '稍后重试',
      refreshPage: '刷新页面重试',
      contactSupport: '如果问题持续，请联系支持',
    },
    placeholders: {
      emptyTitle: '✨ 输入正则表达式开始',
      emptyDescription: '在左侧输入框中输入正则表达式，\n系统将自动生成可视化图表',
      errorTitle: '解析出错',
      inputPrompt: '修改正则表达式试试...',
    },
    performance: {
      parseTime: '解析耗时',
      layoutTime: '布局耗时',
      totalTime: '总耗时',
      nodeCount: '节点数量',
      edgeCount: '边数量',
      complexity: '复杂度',
    },
    debug: {
      hoverTestPanel: '悬停测试面板',
      currentHoveredElementId: '当前悬停的元素ID',
      aiExplanationCacheSize: 'AI 解释缓存数量',
      cacheHitRate: '缓存命中率',
      currentHoveredElementExplanation: '当前悬停元素的解释',
      none: '无',
      loading: '正在加载',
      notAvailable: '不可用',
    },
    explanation: {
      title: '解释面板',
      hoverToSeeExplanation: '悬停在图形节点上查看 AI 解释',
      loadingExplanation: '正在获取 AI 解释...',
      aiExplanation: 'AI 解释',
      noExplanationAvailable: '暂无解释信息',
      currentElement: '当前元素',
      retry: '重试',
    },
  },
  'en-US': {
    errors: {
      parseError: 'Regex Parse Error',
      layoutError: 'Layout Calculation Error',
      networkError: 'Network Connection Error',
      unknownError: 'Unknown Error',
      regexParseFailure: 'Regular expression parsing failed',
      layoutCalculationFailed: 'Layout calculation failed',
      outOfMemory: 'Out of memory',
      maxCallStack: 'Maximum call stack exceeded',
      scriptError: 'Script error',
      elkLoadFailed: 'Failed to load ELK layout engine',
      transformError: 'AST transformation error',
      maxDepthExceeded: 'Maximum recursion depth reached, possible circular reference or overly complex expression',
      aiExplanationFailed: 'Failed to fetch AI explanation',
    },
    suggestions: {
      checkSyntax: 'Check if the regular expression syntax is correct',
      checkBrackets: 'Check for unclosed parentheses or square brackets',
      checkInvalidChars: 'Check for invalid regular expression characters',
      simplifyRegex: 'Try using a simpler regular expression',
      simplifyStructure: 'Try simplifying the regular expression structure',
      checkNesting: 'Check for excessive nesting',
      checkNetwork: 'Check network connection',
      retryLater: 'Retry later',
      refreshPage: 'Refresh the page and try again',
      contactSupport: 'Contact support if the problem persists',
    },
    placeholders: {
      emptyTitle: '✨ Enter a regular expression to start',
      emptyDescription: 'Enter a regular expression in the input box on the left,\nand the system will automatically generate a visualization chart',
      errorTitle: 'Parsing Error',
      inputPrompt: 'Try modifying the regular expression...',
    },
    performance: {
      parseTime: 'Parse Time',
      layoutTime: 'Layout Time',
      totalTime: 'Total Time',
      nodeCount: 'Node Count',
      edgeCount: 'Edge Count',
      complexity: 'Complexity',
    },
    debug: {
      hoverTestPanel: 'Hover Test Panel',
      currentHoveredElementId: 'Current Hovered Element ID',
      aiExplanationCacheSize: 'AI Explanation Cache Size',
      cacheHitRate: 'Cache Hit Rate',
      currentHoveredElementExplanation: 'Current Hovered Element Explanation',
      none: 'None',
      loading: 'Loading',
      notAvailable: 'N/A',
    },
    explanation: {
      title: 'Explanation Panel',
      hoverToSeeExplanation: 'Hover over graph nodes to see AI explanations',
      loadingExplanation: 'Loading AI explanation...',
      aiExplanation: 'AI Explanation',
      noExplanationAvailable: 'No explanation available',
      currentElement: 'Current Element',
      retry: 'Retry',
    },
  },
};

/**
 * 当前语言设置
 */
let currentLocale: Locale = 'zh-CN';

/**
 * 缓存未找到的翻译键，避免重复警告
 */
const missingKeyCache = new Set<string>();

/**
 * 缓存已解析的 key path，提升高频调用性能
 */
const translationCache = new Map<string, string>();

/**
 * 设置当前语言
 */
export function setLocale(locale: Locale): void {
  currentLocale = locale;
  // 清空翻译缓存，因为语言变更了
  translationCache.clear();
}

/**
 * 获取当前语言
 */
export function getLocale(): Locale {
  return currentLocale;
}

/**
 * 获取翻译文本
 */
export function t(key: string): string {
  // 检查缓存，提升高频调用性能
  const cacheKey = `${currentLocale}:${key}`;
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey)!;
  }

  const keys = key.split('.');
  let value: unknown = translations[currentLocale];

  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = (value as Record<string, unknown>)[k];
    } else {
      // 使用缓存避免重复警告，仅在开发环境显示警告
      if (!missingKeyCache.has(key)) {
        if (process.env.NODE_ENV !== "production") {
          console.warn(`Translation key not found: ${key}`);
        }
        missingKeyCache.add(key);
      }
      // 缓存未找到的键，避免重复解析
      translationCache.set(cacheKey, key);
      return key; // 返回原始 key 作为后备
    }
  }

  const result = typeof value === 'string' ? value : key;
  // 缓存成功解析的翻译
  translationCache.set(cacheKey, result);
  return result;
}

/**
 * React Hook 用于获取翻译函数
 */
export function useTranslation() {
  return {
    t,
    locale: currentLocale,
    setLocale,
  };
}
