/**
 * 性能工具函数
 * 提供跨平台的时间和内存测量功能
 */

/**
 * 获取高精度时间戳
 * 浏览器环境使用 performance.now()，Node.js 环境降级到 Date.now()
 */
export function getHighResTime(): number {
  if (typeof performance !== 'undefined' && performance.now) {
    return performance.now();
  }
  return Date.now();
}

/**
 * 获取内存使用情况
 * Node.js 环境返回堆内存使用量，浏览器环境返回 undefined
 */
export function getNodeHeapUsed(): number | undefined {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    return process.memoryUsage().heapUsed;
  }
  return undefined;
}

/**
 * 获取浏览器内存使用情况（如果支持）
 */
export function getBrowserMemoryUsage(): number | undefined {
  // @ts-ignore - performance.memory 可能不存在
  if (typeof performance !== 'undefined' && performance.memory) {
    // @ts-ignore
    return performance.memory.usedJSHeapSize;
  }
  return undefined;
}

/**
 * 获取当前内存使用情况
 * 优先使用 Node.js 的 process.memoryUsage，其次使用浏览器的 performance.memory
 */
export function getMemoryUsage(): number | undefined {
  const nodeMemory = getNodeHeapUsed();
  if (nodeMemory !== undefined) {
    return nodeMemory;
  }
  
  return getBrowserMemoryUsage();
}

/**
 * 格式化内存大小
 * @param bytes 字节数
 * @returns 格式化后的字符串
 */
export function formatMemorySize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 格式化时间
 * @param ms 毫秒数
 * @returns 格式化后的字符串
 */
export function formatTime(ms: number): string {
  if (ms < 1000) {
    return `${ms.toFixed(2)}ms`;
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(ms / 60000);
    const seconds = ((ms % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * 性能测量装饰器
 * @param name 操作名称
 * @param fn 要测量的函数
 * @returns 包装后的函数
 */
export function measurePerformance<T extends (...args: any[]) => any>(
  name: string,
  fn: T
): T {
  return ((...args: Parameters<T>) => {
    const startTime = getHighResTime();
    const startMemory = getMemoryUsage();
    
    try {
      const result = fn(...args);
      
      // 如果是 Promise，等待完成后测量
      if (result instanceof Promise) {
        return result.finally(() => {
          const endTime = getHighResTime();
          const endMemory = getMemoryUsage();
          
          const duration = endTime - startTime;
          const memoryDelta = endMemory && startMemory ? endMemory - startMemory : undefined;
          
          // 异步性能调试日志已禁用
        });
      } else {
        const endTime = getHighResTime();
        const endMemory = getMemoryUsage();
        
        const duration = endTime - startTime;
        const memoryDelta = endMemory && startMemory ? endMemory - startMemory : undefined;
        
        // 同步性能调试日志已禁用
        
        return result;
      }
    } catch (error) {
      const endTime = getHighResTime();
      const duration = endTime - startTime;
      
      // 错误性能日志已禁用
      throw error;
    }
  }) as T;
}

/**
 * 简单的性能计时器
 */
export class PerformanceTimer {
  private startTime: number;
  private name: string;
  private startMemory?: number;

  constructor(name: string) {
    this.name = name;
    this.startTime = getHighResTime();
    this.startMemory = getMemoryUsage();
  }

  /**
   * 结束计时并输出结果
   */
  end(): { duration: number; memoryDelta?: number } {
    const endTime = getHighResTime();
    const endMemory = getMemoryUsage();
    
    const duration = endTime - this.startTime;
    const memoryDelta = endMemory && this.startMemory ? endMemory - this.startMemory : undefined;
    
    // 计时器调试日志已禁用
    
    return { duration, memoryDelta };
  }

  /**
   * 获取当前经过的时间
   */
  elapsed(): number {
    return getHighResTime() - this.startTime;
  }
}

/**
 * 创建性能计时器的便捷函数
 */
export function createTimer(name: string): PerformanceTimer {
  return new PerformanceTimer(name);
}
