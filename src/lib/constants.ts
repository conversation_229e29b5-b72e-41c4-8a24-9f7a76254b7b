/**
 * 应用程序常量配置
 */

/**
 * 布局相关常量
 */
export const LAYOUT_CONSTANTS = {
  // 性能限制（支持环境变量覆盖，便于 A/B 测试）
  MAX_NODES: parseInt(process.env.NEXT_PUBLIC_MAX_NODES || '300', 10),
  MAX_RECURSION_DEPTH: parseInt(process.env.NEXT_PUBLIC_MAX_RECURSION_DEPTH || '50', 10),

  // 布局间距限制
  MAX_SPACING: {
    NODE_NODE: parseInt(process.env.NEXT_PUBLIC_NODE_SPACING || '100', 10),
    LAYER_SPACING: parseInt(process.env.NEXT_PUBLIC_LAYER_SPACING || '150', 10)
  },

  // 性能警告阈值（支持环境变量调整）
  PERFORMANCE_WARNING_THRESHOLD: parseFloat(process.env.NEXT_PUBLIC_PERF_WARNING_THRESHOLD || '0.8')
} as const;

/**
 * 日志级别
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

/**
 * 环境检查
 */
export const IS_PRODUCTION = process.env.NODE_ENV === 'production';
export const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';

/**
 * ELK 布局选项
 */
export const ELK_LAYOUT_OPTIONS = {
  'elk.algorithm': 'layered',
  'elk.direction': 'RIGHT',
  'elk.spacing.nodeNode': '80',
  'elk.layered.spacing.nodeNodeBetweenLayers': '100',
  'elk.layered.crossingMinimization.strategy': 'LAYER_SWEEP',
  'elk.layered.nodePlacement.strategy': 'SIMPLE'
} as const;

/**
 * 可配置的布局选项
 */
export interface LayoutConfig {
  maxNodes?: number;
  maxRecursionDepth?: number;
  enableCache?: boolean;
  logLevel?: LogLevel;
  elkOptions?: Record<string, string>;
}

/**
 * 默认布局配置
 */
export const DEFAULT_LAYOUT_CONFIG: Required<LayoutConfig> = {
  maxNodes: LAYOUT_CONSTANTS.MAX_NODES,
  maxRecursionDepth: LAYOUT_CONSTANTS.MAX_RECURSION_DEPTH,
  enableCache: true,
  logLevel: IS_PRODUCTION ? LogLevel.ERROR : LogLevel.INFO,
  elkOptions: ELK_LAYOUT_OPTIONS
};
