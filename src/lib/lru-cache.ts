/**
 * LRU (Least Recently Used) 缓存实现
 * 用于 AI 解释缓存，防止 Map 无限增长
 *
 * 线程安全说明：
 * - 当前实现适用于 JavaScript 单线程环境
 * - 如果将来迁移到 Worker 多线程环境，需要添加锁机制
 */

export interface LRUCacheOptions {
  maxSize: number;
  ttl?: number; // Time to live in milliseconds
}

interface CacheNode<K, V> {
  key: K;
  value: V;
  timestamp: number;
  prev?: CacheNode<K, V>;
  next?: CacheNode<K, V>;
}

export class LRUCache<K, V> {
  private maxSize: number;
  private ttl?: number;
  private cache: Map<K, CacheNode<K, V>>;
  private head?: CacheNode<K, V>;
  private tail?: CacheNode<K, V>;
  private size: number;

  // 命中率统计
  private hitCount: number = 0;
  private requestCount: number = 0;

  constructor(options: LRUCacheOptions) {
    this.maxSize = options.maxSize;
    this.ttl = options.ttl;
    this.cache = new Map();
    this.size = 0;
    this.hitCount = 0;
    this.requestCount = 0;
  }

  /**
   * 获取缓存值
   */
  get(key: K): V | undefined {
    this.requestCount++; // 记录请求次数

    const node = this.cache.get(key);

    if (!node) {
      return undefined;
    }

    // 检查是否过期
    if (this.ttl && Date.now() - node.timestamp > this.ttl) {
      this.delete(key);
      return undefined;
    }

    this.hitCount++; // 记录命中次数

    // 移动到头部（最近使用）
    this.moveToHead(node);

    return node.value;
  }

  /**
   * 设置缓存值
   */
  set(key: K, value: V): void {
    const existingNode = this.cache.get(key);
    
    if (existingNode) {
      // 更新现有节点
      existingNode.value = value;
      existingNode.timestamp = Date.now();
      this.moveToHead(existingNode);
      return;
    }
    
    // 创建新节点
    const newNode: CacheNode<K, V> = {
      key,
      value,
      timestamp: Date.now(),
    };
    
    // 检查是否需要淘汰
    if (this.size >= this.maxSize) {
      this.removeTail();
    }
    
    // 添加到头部
    this.addToHead(newNode);
    this.cache.set(key, newNode);
    this.size++;
  }

  /**
   * 删除缓存项
   */
  delete(key: K): boolean {
    const node = this.cache.get(key);
    
    if (!node) {
      return false;
    }
    
    this.removeNode(node);
    this.cache.delete(key);
    this.size--;
    
    return true;
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.head = undefined;
    this.tail = undefined;
    this.size = 0;
    this.hitCount = 0;
    this.requestCount = 0;
  }

  /**
   * 检查是否包含某个键
   */
  has(key: K): boolean {
    const node = this.cache.get(key);
    
    if (!node) {
      return false;
    }
    
    // 检查是否过期
    if (this.ttl && Date.now() - node.timestamp > this.ttl) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * 获取当前缓存大小
   */
  getSize(): number {
    return this.size;
  }

  /**
   * 获取最大缓存大小
   */
  getMaxSize(): number {
    return this.maxSize;
  }

  /**
   * 获取所有键
   */
  keys(): K[] {
    const keys: K[] = [];
    let current = this.head;
    
    while (current) {
      // 检查是否过期
      if (!this.ttl || Date.now() - current.timestamp <= this.ttl) {
        keys.push(current.key);
      }
      current = current.next;
    }
    
    return keys;
  }

  /**
   * 清理过期项
   */
  cleanup(): number {
    if (!this.ttl) {
      return 0;
    }
    
    const now = Date.now();
    const expiredKeys: K[] = [];
    
    for (const [key, node] of this.cache) {
      if (now - node.timestamp > this.ttl) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.delete(key));
    
    return expiredKeys.length;
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate?: number;
    hitCount: number;
    requestCount: number;
    oldestTimestamp?: number;
    newestTimestamp?: number;
  } {
    const hitRate = this.requestCount > 0 ? (this.hitCount / this.requestCount) * 100 : undefined;

    const stats = {
      size: this.size,
      maxSize: this.maxSize,
      hitRate,
      hitCount: this.hitCount,
      requestCount: this.requestCount,
      oldestTimestamp: this.tail?.timestamp,
      newestTimestamp: this.head?.timestamp,
    };

    return stats;
  }

  /**
   * 克隆当前缓存实例
   * 用于 Zustand 状态更新时创建新引用
   * 3. 优化：保留原时间戳，确保精确 TTL
   */
  clone(): LRUCache<K, V> {
    const newCache = new LRUCache<K, V>({
      maxSize: this.maxSize,
      ttl: this.ttl
    });

    // 复制统计信息
    newCache.hitCount = this.hitCount;
    newCache.requestCount = this.requestCount;

    // 复制所有缓存项，保持插入顺序和原始时间戳
    let current = this.tail; // 从最旧的开始
    while (current) {
      // 直接创建节点并设置原始时间戳，而不是调用 set()
      const newNode: CacheNode<K, V> = {
        key: current.key,
        value: current.value,
        timestamp: current.timestamp // 保留原始时间戳
      };

      // 1. 优化：直接调用方法，提高可读性
      newCache.cache.set(newNode.key, newNode);
      newCache.addToHead(newNode); // 直接调用，无需索引写法
      newCache.size++;

      current = current.prev;
    }

    return newCache;
  }

  // 私有方法

  /**
   * 将节点添加到链表头部（最近使用位置）
   * @param node 要添加的节点
   */
  protected addToHead(node: CacheNode<K, V>): void {
    node.prev = undefined;
    node.next = this.head;
    
    if (this.head) {
      this.head.prev = node;
    }
    
    this.head = node;
    
    if (!this.tail) {
      this.tail = node;
    }
  }

  /**
   * 从链表中移除指定节点
   * @param node 要移除的节点
   */
  private removeNode(node: CacheNode<K, V>): void {
    if (node.prev) {
      node.prev.next = node.next;
    } else {
      this.head = node.next;
    }
    
    if (node.next) {
      node.next.prev = node.prev;
    } else {
      this.tail = node.prev;
    }
  }

  /**
   * 将节点移动到链表头部（标记为最近使用）
   * @param node 要移动的节点
   */
  private moveToHead(node: CacheNode<K, V>): void {
    this.removeNode(node);
    this.addToHead(node);
  }

  /**
   * 移除链表尾部节点（最久未使用的节点）
   * 用于缓存淘汰策略
   */
  private removeTail(): void {
    if (!this.tail) {
      return;
    }
    
    const lastNode = this.tail;
    this.removeNode(lastNode);
    this.cache.delete(lastNode.key);
    this.size--;
  }
}
