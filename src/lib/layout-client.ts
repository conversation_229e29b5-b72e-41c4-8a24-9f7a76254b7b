"use client"

import type { <PERSON><PERSON>, <PERSON> } from '@xyflow/react';
import type { AstRegExp } from 'regexp-tree/ast';

import type { LayoutConfig } from './constants';
import { performLayout } from './elk-wrapper';
import { NodeType } from '@/types/graph';
import { logger } from './logger';

/**
 * 根据语义类型决定 React Flow 节点类型
 */
function getReactFlowNodeType(semanticType: NodeType): string {
  switch (semanticType) {
    case NodeType.MACRO_SEQUENCE:
      return 'macro_sequence';
    default:
      return 'regexNode';
  }
}

// 重新导出 layout 函数，但使用动态导入的 ELK
export async function getLayoutedElements(
  ast: AstRegExp,
  config: Partial<LayoutConfig> = {}
): Promise<{ nodes: Node[]; edges: Edge[] }> {
  // 动态导入其他依赖
  const [{ astToElkGraph }, { DEFAULT_LAYOUT_CONFIG }, { logger }] = await Promise.all([
    import('./transformer'),
    import('./constants'),
    import('./logger')
  ]);

  try {
    // 验证输入
    if (!ast) {
      throw new Error('AST 对象不能为空');
    }

    // 合并配置
    const finalConfig = { ...DEFAULT_LAYOUT_CONFIG, ...config };



    // 调用 astToElkGraph(ast) 得到 ELK 图结构
    const graph = astToElkGraph(ast);

    // 空图处理 - 返回空状态
    if (!graph.children || graph.children.length === 0) {

      return { nodes: [], edges: [] };
    }

    // 将配置的布局选项应用到图结构
    const optimizedGraph = {
      ...graph,
      layoutOptions: {
        ...graph.layoutOptions,
        ...finalConfig.elkOptions
      }
    };

    // 调用 performLayout 来进行布局计算
    const layoutedGraph = await performLayout(optimizedGraph);





    // 提取节点和边
    const nodes: Node[] = [];
    const edges: Edge[] = [];

    // 处理节点
    if (layoutedGraph.children) {
      for (const child of layoutedGraph.children) {
        // 类型断言来访问自定义数据
        const customChild = child as any;
        const nodeData = customChild.data || {
          semanticType: 'literal' as any,
          content: 'Unknown',
          originalText: 'Unknown',
          astNodeType: 'Unknown'
        };

        // 根据语义类型决定 React Flow 节点类型
        const reactFlowNodeType = getReactFlowNodeType(nodeData.semanticType);

        // 基于内容长度估算宽度，避免标签遮挡
        const estimatedWidth = Math.max(80, nodeData.content.length * 8 + 20);

        // 更精确的高度计算：基于实际行数
        const lines = nodeData.content.split('\n');
        const estimatedHeight = Math.max(40, lines.length * 18 + 20);

        const reactFlowNode = {
          id: child.id,
          type: reactFlowNodeType, // 使用动态决定的节点类型
          position: {
            x: child.x || 0,
            y: child.y || 0
          },
          data: {
            label: nodeData.content,
            ...nodeData
          },
          style: {
            width: child.width || estimatedWidth,
            height: child.height || estimatedHeight,
            fontSize: '14px', // 统一字体大小，供外层主题覆盖
          },
          // 🔧 [连接修复] 显式设置节点尺寸，确保React Flow正确计算连接点
          width: child.width || estimatedWidth,
          height: child.height || estimatedHeight
        };





        nodes.push(reactFlowNode);
      }
    }

    // 节点ID唯一性检查
    const allNodeIds = new Set(nodes.map(node => node.id));

    // 处理边
    if (layoutedGraph.edges) {

      for (const edge of layoutedGraph.edges) {

        if (edge.sources && edge.targets) {
          for (let i = 0; i < edge.sources.length; i++) {
            for (let j = 0; j < edge.targets.length; j++) {
              const edgeId = edge.sources.length === 1 && edge.targets.length === 1
                ? edge.id
                : `${edge.id}_${i}_${j}`;

              const reactFlowEdge = {
                id: edgeId,
                source: edge.sources[i],
                target: edge.targets[j],
                sourceHandle: 'source', // 指定源Handle ID
                targetHandle: 'target', // 指定目标Handle ID
                type: 'default',
                animated: false
              };





              edges.push(reactFlowEdge);
            }
          }
        }
      }
    }



    // 最终数据完整性检查
    const finalNodeIds = new Set(nodes.map(n => n.id));
    const orphanEdges = edges.filter(e => !finalNodeIds.has(e.source) || !finalNodeIds.has(e.target));







    return { nodes, edges };

  } catch (error) {



    throw new Error(`布局计算失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}
