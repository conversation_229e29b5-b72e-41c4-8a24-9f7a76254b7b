/**
 * 性能监控模块
 */

import { LAYOUT_CONSTANTS } from './constants';
import { getMemoryUsage, getHighResTime } from './utils/performance';
import { PERFORMANCE_CONFIG } from './config';

export interface PerformanceMetrics {
  parseTime: number;
  layoutTime: number;
  totalTime: number;
  nodeCount: number;
  edgeCount: number;
  complexity: number;
  timestamp: number;
  regexLength: number;
  memoryUsage?: number;
}

export interface PerformanceThresholds {
  parseTime: number;
  layoutTime: number;
  totalTime: number;
  nodeCount: number;
  complexity: number;
}

// 性能监控配置已移至 config.ts

/**
 * 默认性能阈值
 */
const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  parseTime: PERFORMANCE_CONFIG.WARNING_THRESHOLDS.PARSE_TIME,
  layoutTime: PERFORMANCE_CONFIG.WARNING_THRESHOLDS.LAYOUT_TIME,
  totalTime: PERFORMANCE_CONFIG.WARNING_THRESHOLDS.TOTAL_TIME,
  nodeCount: PERFORMANCE_CONFIG.WARNING_THRESHOLDS.NODE_COUNT,
  complexity: PERFORMANCE_CONFIG.WARNING_THRESHOLDS.COMPLEXITY,
};

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private thresholds: PerformanceThresholds;
  private maxHistorySize: number;

  // 增量统计维护，避免每次 O(n) 计算
  private totalParseTime: number = 0;
  private totalLayoutTime: number = 0;
  private totalTime: number = 0;
  private totalNodeCount: number = 0;

  constructor(
    thresholds: Partial<PerformanceThresholds> = {},
    maxHistorySize: number = PERFORMANCE_CONFIG.MAX_HISTORY_SIZE
  ) {
    this.thresholds = { ...DEFAULT_THRESHOLDS, ...thresholds };
    this.maxHistorySize = maxHistorySize;
  }

  /**
   * 创建性能计时器
   */
  createTimer(label: string = 'default'): PerformanceTimer {
    return new PerformanceTimer(label);
  }

  /**
   * 记录性能指标
   */
  recordMetrics(metrics: Omit<PerformanceMetrics, 'timestamp'>): void {
    const fullMetrics: PerformanceMetrics = {
      ...metrics,
      timestamp: Date.now(),
    };

    // 更新增量统计
    this.totalParseTime += fullMetrics.parseTime;
    this.totalLayoutTime += fullMetrics.layoutTime;
    this.totalTime += fullMetrics.totalTime;
    this.totalNodeCount += fullMetrics.nodeCount;

    this.metrics.push(fullMetrics);

    // 保持历史记录在限制内，防止内存泄漏
    if (this.metrics.length > this.maxHistorySize) {
      // 移除最旧的记录并更新增量统计
      const removed = this.metrics.splice(0, this.metrics.length - this.maxHistorySize);

      // 从增量统计中减去被移除的记录
      for (const removedMetric of removed) {
        this.totalParseTime -= removedMetric.parseTime;
        this.totalLayoutTime -= removedMetric.layoutTime;
        this.totalTime -= removedMetric.totalTime;
        this.totalNodeCount -= removedMetric.nodeCount;
      }

      if (process.env.NODE_ENV === 'development') {
        // 清理日志已禁用
      }
    }

    // 检查性能警告
    this.checkPerformanceWarnings(fullMetrics);

    // 在开发环境中输出性能信息
    if (process.env.NODE_ENV === 'development') {
      this.logPerformanceInfo(fullMetrics);
    }
  }

  /**
   * 获取性能统计
   */
  getStats(): {
    average: Partial<PerformanceMetrics>;
    latest: PerformanceMetrics | null;
    count: number;
    warnings: string[];
  } {
    if (this.metrics.length === 0) {
      return {
        average: {},
        latest: null,
        count: 0,
        warnings: [],
      };
    }

    const latest = this.metrics[this.metrics.length - 1];
    const average = this.calculateAverageIncremental();
    const warnings = this.getPerformanceWarnings(latest);

    return {
      average,
      latest,
      count: this.metrics.length,
      warnings,
    };
  }

  /**
   * 清除历史记录
   */
  clearHistory(): void {
    this.metrics = [];
    // 重置增量统计
    this.totalParseTime = 0;
    this.totalLayoutTime = 0;
    this.totalTime = 0;
    this.totalNodeCount = 0;
  }

  /**
   * 使用增量统计计算平均值（O(1) 复杂度）
   */
  private calculateAverageIncremental(): Partial<PerformanceMetrics> {
    const count = this.metrics.length;
    if (count === 0) return {};

    return {
      parseTime: Math.round(this.totalParseTime / count),
      layoutTime: Math.round(this.totalLayoutTime / count),
      totalTime: Math.round(this.totalTime / count),
      nodeCount: Math.round(this.totalNodeCount / count),
    };
  }

  /**
   * 获取所有历史记录
   */
  getHistory(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * 获取内存使用统计
   */
  getMemoryStats(): {
    currentUsage: number | undefined;
    averageUsage: number | undefined;
    peakUsage: number | undefined;
  } {
    const currentUsage = getMemoryUsage();

    const memoryMetrics = this.metrics
      .map(m => m.memoryUsage)
      .filter((usage): usage is number => usage !== undefined);

    if (memoryMetrics.length === 0) {
      return {
        currentUsage,
        averageUsage: undefined,
        peakUsage: undefined,
      };
    }

    const averageUsage = memoryMetrics.reduce((sum, usage) => sum + usage, 0) / memoryMetrics.length;
    const peakUsage = Math.max(...memoryMetrics);

    return {
      currentUsage,
      averageUsage: Math.round(averageUsage),
      peakUsage,
    };
  }

  /**
   * 检查是否需要清理内存
   */
  shouldCleanupMemory(): boolean {
    const memoryStats = this.getMemoryStats();
    if (!memoryStats.currentUsage || !memoryStats.averageUsage) {
      return false;
    }

    // 如果当前内存使用量超过平均值的 150%，建议清理
    return memoryStats.currentUsage > memoryStats.averageUsage * 1.5;
  }



  /**
   * 检查性能警告
   */
  private checkPerformanceWarnings(metrics: PerformanceMetrics): void {
    const warnings = this.getPerformanceWarnings(metrics);
    if (warnings.length > 0) {
      console.warn('Performance warnings:', warnings);
    }
  }

  /**
   * 获取性能警告
   */
  private getPerformanceWarnings(metrics: PerformanceMetrics): string[] {
    const warnings: string[] = [];

    if (metrics.parseTime > this.thresholds.parseTime) {
      warnings.push(`Parse time (${metrics.parseTime}ms) exceeds threshold (${this.thresholds.parseTime}ms)`);
    }

    if (metrics.layoutTime > this.thresholds.layoutTime) {
      warnings.push(`Layout time (${metrics.layoutTime}ms) exceeds threshold (${this.thresholds.layoutTime}ms)`);
    }

    if (metrics.totalTime > this.thresholds.totalTime) {
      warnings.push(`Total time (${metrics.totalTime}ms) exceeds threshold (${this.thresholds.totalTime}ms)`);
    }

    if (metrics.nodeCount > this.thresholds.nodeCount) {
      warnings.push(`Node count (${metrics.nodeCount}) exceeds threshold (${this.thresholds.nodeCount})`);
    }

    if (metrics.complexity > this.thresholds.complexity) {
      warnings.push(`Complexity (${metrics.complexity}) exceeds threshold (${this.thresholds.complexity})`);
    }

    return warnings;
  }

  /**
   * 输出性能信息（已禁用）
   */
  private logPerformanceInfo(metrics: PerformanceMetrics): void {
    // 性能日志已禁用，避免控制台输出
  }
}

/**
 * 性能计时器类
 */
export class PerformanceTimer {
  private startTime: number;
  private endTime?: number;
  private marks: Map<string, number> = new Map();

  constructor(private label: string) {
    this.startTime = getHighResTime();
  }

  /**
   * 标记时间点
   */
  mark(name: string): void {
    this.marks.set(name, getHighResTime());
  }

  /**
   * 获取从开始到指定标记的时间
   */
  getTimeToMark(name: string): number {
    const markTime = this.marks.get(name);
    if (!markTime) {
      throw new Error(`Mark "${name}" not found`);
    }
    return Math.round(markTime - this.startTime);
  }

  /**
   * 获取两个标记之间的时间
   */
  getTimeBetweenMarks(startMark: string, endMark: string): number {
    const startTime = this.marks.get(startMark);
    const endTime = this.marks.get(endMark);
    
    if (!startTime || !endTime) {
      throw new Error(`Mark "${startMark}" or "${endMark}" not found`);
    }
    
    return Math.round(endTime - startTime);
  }

  /**
   * 结束计时
   */
  end(): number {
    this.endTime = getHighResTime();
    return this.getTotalTime();
  }

  /**
   * 获取总时间
   */
  getTotalTime(): number {
    const endTime = this.endTime || getHighResTime();
    return Math.round(endTime - this.startTime);
  }

  /**
   * 获取所有标记
   */
  getAllMarks(): Record<string, number> {
    const result: Record<string, number> = {};
    for (const [name, time] of this.marks) {
      result[name] = Math.round(time - this.startTime);
    }
    return result;
  }
}

/**
 * 全局性能监控器实例
 */
export const performanceMonitor = new PerformanceMonitor();



/**
 * 计算复杂度分数
 */
export function calculateComplexity(nodeCount: number, edgeCount: number, regexLength: number): number {
  // 简单的复杂度计算公式
  return Math.round(
    nodeCount * 1.0 +
    edgeCount * 0.5 +
    regexLength * 0.1
  );
}
