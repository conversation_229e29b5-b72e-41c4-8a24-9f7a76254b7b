import { parse } from 'regexp-tree';
import type { AstRegExp } from 'regexp-tree/ast';
import { ParseRegexResult, ASTNode } from '../types/global';
import { LRUCache } from './lru-cache';
import { logger } from './logger';

// 使用 LRU 缓存来缓存解析结果，防止内存泄漏
const regexCache = new LRUCache<string, AstRegExp>({
  maxSize: 100,
  ttl: 30 * 60 * 1000 // 30 minutes
});

/**
 * 正则表达式解析结果接口（向后兼容）
 * @deprecated 使用 ParseRegexResult 替代
 */
export interface ParseResult {
  ast: AstRegExp | null;
  error: string | null;
}

/**
 * 解析正则表达式字符串为 AST (抽象语法树)
 *
 * @param regexString - 要解析的正则表达式字符串
 * @returns 包含 AST 对象和错误信息的结果对象
 */
export function parseRegex(regexString: string): ParseResult {

  // 空字符串快速返回
  if (!regexString.trim()) {
    return { ast: null, error: '正则表达式不能为空' };
  }

  // 检查缓存
  const cachedAst = regexCache.get(regexString);
  if (cachedAst) {
    return { ast: cachedAst, error: null };
  }

  try {
    // regexp-tree 默认要求 JS 字面量格式，例如 /abc/。
    // 但大多数调用者仅传入裸模式（如 "abc"）。
    // 如果字符串不以斜杠开头，则自动包装为 /pattern/ 并转义内部斜杠，
    // 这样即可无缝解析简单模式，避免 "Unexpected token" 错误。
    const literalLike = regexString.trim().startsWith('/')
      ? regexString.trim()
      : `/${regexString.replace(/\//g, '\\/')}/`;



    // 使用 regexp-tree 解析正则表达式（支持裸模式及正则字面量）
    const ast = parse(literalLike);

    // 缓存解析结果（LRU 缓存会自动管理大小）
    regexCache.set(regexString, ast);

    return { ast, error: null };
  } catch (error) {
    // 解析失败时提取错误信息
    const errorMessage = error instanceof Error ? error.message : '未知解析错误';

    // DEBUG: 记录详细错误信息
    logger.error('正则表达式解析失败', {
      input: regexString,
      errorMessage,
      errorStack: error instanceof Error ? error.stack : 'No stack',
      errorName: error instanceof Error ? error.name : 'Unknown'
    });

    return { ast: null, error: errorMessage };
  }
}

/**
 * 清除解析缓存
 * 在内存使用过多时可以调用此函数清理缓存
 */
export function clearParseCache(): void {
  regexCache.clear();
}

/**
 * 获取当前缓存的统计信息
 * @returns 缓存大小
 */
/**
 * 增强的正则表达式解析函数
 * 返回更详细的解析结果，包括复杂度和节点数量
 *
 * @param regexString - 要解析的正则表达式字符串
 * @returns 详细的解析结果
 */
export function parseRegexEnhanced(regexString: string): ParseRegexResult {
  const basicResult = parseRegex(regexString);

  if (basicResult.error || !basicResult.ast) {
    return {
      ast: null,
      error: basicResult.error,
      complexity: 0,
      nodeCount: 0
    };
  }

  // 计算复杂度和节点数量
  const complexity = calculateComplexity(basicResult.ast);
  const nodeCount = countNodes(basicResult.ast);

  return {
    ast: basicResult.ast as unknown as ASTNode,
    error: null,
    complexity,
    nodeCount
  };
}

/**
 * 计算正则表达式的复杂度
 *
 * @param ast - AST 节点
 * @returns 复杂度分数
 */
function calculateComplexity(ast: AstRegExp): number {
  let complexity = 0;

  // 递归遍历 AST 计算复杂度
  function traverse(node: any): void {
    if (!node) return;

    switch (node.type) {
      case 'Quantifier':
        complexity += 2;
        break;
      case 'Group':
        complexity += 1;
        break;
      case 'Alternative':
        complexity += 3;
        break;
      case 'Assertion':
        complexity += 4;
        break;
      case 'Backreference':
        complexity += 5;
        break;
      default:
        complexity += 1;
    }

    // 递归处理子节点
    if (node.body) traverse(node.body);
    if (node.expressions) {
      node.expressions.forEach((expr: any) => traverse(expr));
    }
    if (node.expression) traverse(node.expression);
    if (node.alternatives) {
      node.alternatives.forEach((alt: any) => traverse(alt));
    }
  }

  traverse(ast);
  return complexity;
}

/**
 * 计算 AST 节点数量
 *
 * @param ast - AST 节点
 * @returns 节点数量
 */
function countNodes(ast: AstRegExp): number {
  let count = 0;

  function traverse(node: any): void {
    if (!node) return;

    count++;

    // 递归处理子节点
    if (node.body) traverse(node.body);
    if (node.expressions) {
      node.expressions.forEach((expr: any) => traverse(expr));
    }
    if (node.expression) traverse(node.expression);
    if (node.alternatives) {
      node.alternatives.forEach((alt: any) => traverse(alt));
    }
  }

  traverse(ast);
  return count;
}

export function getCacheStats(): { size: number; maxSize: number } {
  return {
    size: regexCache.getSize(),
    maxSize: regexCache.getMaxSize()
  };
}
