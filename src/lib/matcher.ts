/**
 * 正则表达式匹配工具
 * 用于处理测试字符串与正则表达式的匹配，并返回匹配结果的位置信息
 */

/**
 * 匹配结果类型定义
 */
export interface MatchResult {
  /** 匹配的文本内容 */
  text: string;
  /** 匹配开始位置（包含） */
  startIndex: number;
  /** 匹配结束位置（不包含） */
  endIndex: number;
  /** 匹配索引（第几个匹配） */
  matchIndex: number;
  /** 捕获组信息 */
  groups?: string[];
}

/**
 * 匹配错误类型
 */
export interface MatchError {
  type: 'INVALID_REGEX' | 'RUNTIME_ERROR';
  message: string;
}

/**
 * 匹配函数返回结果
 */
export interface MatchResponse {
  success: boolean;
  matches: MatchResult[];
  error?: MatchError;
  totalMatches: number;
}

/**
 * 获取正则表达式在测试字符串中的所有匹配结果
 * 
 * @param regexString - 正则表达式字符串
 * @param testString - 测试字符串
 * @returns 匹配结果对象
 */
export function getMatches(regexString: string, testString: string): MatchResponse {
  // 处理空输入情况
  if (!regexString.trim() || !testString.trim()) {
    return {
      success: true,
      matches: [],
      totalMatches: 0
    };
  }

  try {
    // 创建正则表达式对象，添加全局标志以获取所有匹配
    let regex: RegExp;
    
    try {
      // 检查正则表达式是否已经包含标志
      const lastSlashIndex = regexString.lastIndexOf('/');
      const firstSlashIndex = regexString.indexOf('/');
      
      if (firstSlashIndex === 0 && lastSlashIndex > 0 && lastSlashIndex !== firstSlashIndex) {
        // 正则表达式格式：/pattern/flags
        const pattern = regexString.slice(1, lastSlashIndex);
        const flags = regexString.slice(lastSlashIndex + 1);
        
        // 确保包含全局标志
        const globalFlags = flags.includes('g') ? flags : flags + 'g';
        regex = new RegExp(pattern, globalFlags);
      } else {
        // 纯模式字符串，添加全局标志
        regex = new RegExp(regexString, 'g');
      }
    } catch (syntaxError) {
      return {
        success: false,
        matches: [],
        totalMatches: 0,
        error: {
          type: 'INVALID_REGEX',
          message: `正则表达式语法错误: ${(syntaxError as Error).message}`
        }
      };
    }

    const matches: MatchResult[] = [];
    let matchIndex = 0;
    let match: RegExpExecArray | null;

    // 使用 exec 方法获取所有匹配
    while ((match = regex.exec(testString)) !== null) {
      const matchResult: MatchResult = {
        text: match[0],
        startIndex: match.index,
        endIndex: match.index + match[0].length,
        matchIndex: matchIndex++,
        groups: match.length > 1 ? match.slice(1) : undefined
      };

      matches.push(matchResult);

      // 防止无限循环（零宽度匹配的情况）
      if (match[0].length === 0) {
        regex.lastIndex++;
      }

      // 安全检查：防止过多匹配导致性能问题
      if (matches.length > 1000) {
        break;
      }
    }

    return {
      success: true,
      matches,
      totalMatches: matches.length
    };

  } catch (runtimeError) {
    return {
      success: false,
      matches: [],
      totalMatches: 0,
      error: {
        type: 'RUNTIME_ERROR',
        message: `匹配执行错误: ${(runtimeError as Error).message}`
      }
    };
  }
}

/**
 * 检查正则表达式是否有效
 * 
 * @param regexString - 正则表达式字符串
 * @returns 是否有效
 */
export function isValidRegex(regexString: string): boolean {
  if (!regexString.trim()) return false;
  
  try {
    // 处理带斜杠的正则表达式格式
    const lastSlashIndex = regexString.lastIndexOf('/');
    const firstSlashIndex = regexString.indexOf('/');
    
    if (firstSlashIndex === 0 && lastSlashIndex > 0 && lastSlashIndex !== firstSlashIndex) {
      const pattern = regexString.slice(1, lastSlashIndex);
      const flags = regexString.slice(lastSlashIndex + 1);
      new RegExp(pattern, flags);
    } else {
      new RegExp(regexString);
    }
    
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取匹配统计信息
 * 
 * @param matches - 匹配结果数组
 * @returns 统计信息
 */
export function getMatchStats(matches: MatchResult[]) {
  if (matches.length === 0) {
    return {
      totalMatches: 0,
      totalLength: 0,
      averageLength: 0,
      coverage: 0
    };
  }

  const totalLength = matches.reduce((sum, match) => sum + match.text.length, 0);
  const averageLength = totalLength / matches.length;

  return {
    totalMatches: matches.length,
    totalLength,
    averageLength: Math.round(averageLength * 100) / 100,
    coverage: matches.length > 0 ? Math.round((totalLength / matches.length) * 100) / 100 : 0
  };
}
