import adze, { setup } from 'adze';

// 根据环境变量决定日志格式
const isDevelopment = process.env.NODE_ENV === 'development';
const isTest = process.env.NODE_ENV === 'test';
const format = isDevelopment ? 'pretty' : 'json';

// 根据环境变量设置日志级别，默认为 'info'
const activeLevel = process.env.LOG_LEVEL?.toLowerCase() || 'info';

// 全局配置 Adze 日志库
const store = setup({
  activeLevel,
  format,
  cache: true, // 启用日志缓存
  cacheSize: 500, // 设置缓存大小
  withEmoji: isDevelopment, // 开发环境启用 emoji
  showTimestamp: true, // 显示时间戳
  silent: isTest, // 测试环境静默日志
  meta: {
    app: 'RegexAI',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  },
});

// 添加日志监听器，用于处理不同级别的日志
store.addListener('error', (log) => {
  // 可以在这里添加错误日志的特殊处理，比如发送到监控服务
  if (isDevelopment) {
    console.error('🚨 Error logged:', log.data);
  }
});

store.addListener('alert', (log) => {
  // 可以在这里添加警报日志的特殊处理，比如发送通知
  if (isDevelopment) {
    console.warn('⚠️ Alert logged:', log.data);
  }
});

// 创建不同用途的日志实例
const baseLogger = adze.withEmoji.timestamp.ns('RegexAI').seal();

// API 相关日志
const apiLogger = adze.withEmoji.timestamp.ns('RegexAI', 'API').seal();

// 性能相关日志
const performanceLogger = adze.withEmoji.timestamp.ns('RegexAI', 'Performance').seal();

// 用户交互日志
const uiLogger = adze.withEmoji.timestamp.ns('RegexAI', 'UI').seal();

// 正则表达式处理日志
const regexLogger = adze.withEmoji.timestamp.ns('RegexAI', 'Regex').seal();

// 便捷的日志方法
export const adzeLog = {
  // 基础日志方法
  debug: (message: string, ...args: any[]) => baseLogger.debug(message, ...args),
  info: (message: string, ...args: any[]) => baseLogger.info(message, ...args),
  warn: (message: string, ...args: any[]) => baseLogger.warn(message, ...args),
  error: (message: string, error?: Error, ...args: any[]) => {
    if (error) {
      baseLogger.error(message, error, ...args);
    } else {
      baseLogger.error(message, ...args);
    }
  },
  success: (message: string, ...args: any[]) => baseLogger.success(message, ...args),
  fail: (message: string, ...args: any[]) => baseLogger.fail(message, ...args),

  // API 相关日志
  api: {
    request: (method: string, path: string, ...args: any[]) => 
      apiLogger.info(`${method} ${path}`, ...args),
    response: (method: string, path: string, status: number, duration?: number, ...args: any[]) => {
      const message = `${method} ${path} ${status}${duration ? ` (${duration}ms)` : ''}`;
      if (status >= 400) {
        apiLogger.warn(message, ...args);
      } else {
        apiLogger.success(message, ...args);
      }
    },
    error: (method: string, path: string, error: Error, ...args: any[]) =>
      apiLogger.error(`${method} ${path} failed`, error, ...args),
  },

  // 性能日志
  performance: {
    start: (operation: string) => {
      const startTime = performance.now();
      return {
        end: (...args: any[]) => {
          const duration = performance.now() - startTime;
          performanceLogger.debug(`${operation} completed in ${duration.toFixed(2)}ms`, ...args);
          return duration;
        }
      };
    },
    measure: (operation: string, duration: number, ...args: any[]) =>
      performanceLogger.debug(`${operation} took ${duration.toFixed(2)}ms`, ...args),
  },

  // UI 交互日志
  ui: {
    click: (element: string, ...args: any[]) => uiLogger.debug(`Clicked: ${element}`, ...args),
    input: (field: string, value?: string, ...args: any[]) => 
      uiLogger.debug(`Input changed: ${field}${value ? ` = ${value}` : ''}`, ...args),
    navigation: (from: string, to: string, ...args: any[]) =>
      uiLogger.info(`Navigation: ${from} → ${to}`, ...args),
    error: (component: string, error: Error, ...args: any[]) =>
      uiLogger.error(`UI Error in ${component}`, error, ...args),
  },

  // 正则表达式处理日志
  regex: {
    parse: (pattern: string, success: boolean, ...args: any[]) => {
      if (success) {
        regexLogger.success(`Regex parsed: ${pattern}`, ...args);
      } else {
        regexLogger.warn(`Regex parse failed: ${pattern}`, ...args);
      }
    },
    match: (pattern: string, text: string, matches: number, ...args: any[]) =>
      regexLogger.debug(`Regex match: ${pattern} found ${matches} matches in text`, ...args),
    error: (pattern: string, error: Error, ...args: any[]) =>
      regexLogger.error(`Regex error: ${pattern}`, error, ...args),
  },
};

// 导出原始的 adze 实例和存储对象，以便需要时进行高级配置
export { baseLogger as adzeLogger, store as adzeStore };
export default adzeLog;
