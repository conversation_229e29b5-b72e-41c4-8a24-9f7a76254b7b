import { getLayoutedElements, LayoutError, clearLayoutCache } from '../layout';
import { parseRegex } from '../parser';
import { LogLevel } from '../constants';

// 这个文件包含真正的 ELK 集成测试，不使用 Mock
describe('layout.ts Integration Tests', () => {
  beforeEach(() => {
    clearLayoutCache();
  });

  describe('Real ELK Layout Integration', () => {
    it('should perform real layout calculation with ELK', async () => {
      const parseResult = parseRegex('a+b*');
      
      if (!parseResult.ast) {
        throw new Error(`解析失败: ${parseResult.error}`);
      }
      
      const result = await getLayoutedElements(parseResult.ast, {
        logLevel: LogLevel.ERROR // 减少测试输出
      });
      
      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(result.nodes.length).toBeGreaterThan(0);
      
      // 验证节点具有真实的布局位置
      const firstNode = result.nodes[0];
      expect(typeof firstNode.position.x).toBe('number');
      expect(typeof firstNode.position.y).toBe('number');
      expect(firstNode.position.x).toBeGreaterThanOrEqual(0);
      expect(firstNode.position.y).toBeGreaterThanOrEqual(0);
    }, 10000); // 增加超时时间，因为真实布局计算较慢

    it('should handle complex regex with real ELK layout', async () => {
      const parseResult = parseRegex('(abc|def){2,3}\\d+');
      
      if (!parseResult.ast) {
        throw new Error(`解析失败: ${parseResult.error}`);
      }
      
      const result = await getLayoutedElements(parseResult.ast, {
        logLevel: LogLevel.ERROR
      });
      
      expect(result.nodes.length).toBeGreaterThan(3);
      expect(result.edges.length).toBeGreaterThan(0);
      
      // 验证节点位置的合理性
      const positions = result.nodes.map(n => ({ x: n.position.x, y: n.position.y }));
      const uniquePositions = new Set(positions.map(p => `${p.x},${p.y}`));
      
      // 大多数节点应该有不同的位置（允许少量重叠）
      expect(uniquePositions.size).toBeGreaterThan(positions.length * 0.5);
    }, 15000);
  });
});
