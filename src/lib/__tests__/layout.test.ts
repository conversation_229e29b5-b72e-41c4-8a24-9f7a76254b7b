import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getLayoutedElements, LayoutError, clearLayoutCache } from '../layout';
import { parseRegex } from '../parser';
import { LogLevel } from '../constants';

// Mock ELK for faster unit tests
vi.mock('elkjs', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      layout: vi.fn().mockImplementation(async (graph) => ({
        ...graph,
        children: graph.children?.map((child: any, index: number) => ({
          ...child,
          x: index * 100,
          y: 50,
          width: child.width || 60,
          height: child.height || 40
        })) || [],
        edges: graph.edges || []
      }))
    }))
  };
});

describe('layout.ts', () => {
  beforeEach(() => {
    // 清除缓存确保测试独立性
    clearLayoutCache();
  });

  describe('getLayoutedElements', () => {
    it('should process a simple regex and return nodes and edges', async () => {
      // 解析一个简单的正则表达式
      const parseResult = parseRegex('abc');

      // 检查解析是否成功
      if (!parseResult.ast) {
        throw new Error(`解析失败: ${parseResult.error}`);
      }

      // 调用布局函数
      const result = await getLayoutedElements(parseResult.ast);

      // 验证返回结果的结构
      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(Array.isArray(result.nodes)).toBe(true);
      expect(Array.isArray(result.edges)).toBe(true);

      // 验证节点包含必要的属性
      if (result.nodes.length > 0) {
        const firstNode = result.nodes[0];
        expect(firstNode).toHaveProperty('id');
        expect(firstNode).toHaveProperty('position');
        expect(firstNode).toHaveProperty('data');
        expect(firstNode.position).toHaveProperty('x');
        expect(firstNode.position).toHaveProperty('y');
      }
    });

    it('should return empty placeholder for empty regex', async () => {
      // 测试空正则表达式的处理
      const parseResult = parseRegex('');

      // 空正则表达式应该返回 null AST
      expect(parseResult.ast).toBeNull();

      // 但我们需要测试空 AST 的处理，创建一个最小的空 AST
      const emptyAst = {
        type: 'RegExp' as const,
        body: null as any,
        flags: ''
      };

      const result = await getLayoutedElements(emptyAst);

      expect(result.nodes).toHaveLength(1);
      expect(result.nodes[0].id).toBe('empty-placeholder');
      expect(result.nodes[0].data.content).toBe('空正则表达式');
      expect(result.edges).toHaveLength(0);
    });

    it('should handle complex regex with groups and quantifiers', async () => {
      const parseResult = parseRegex('(a+b)*c?');

      if (!parseResult.ast) {
        throw new Error(`解析失败: ${parseResult.error}`);
      }

      const result = await getLayoutedElements(parseResult.ast);

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(result.nodes.length).toBeGreaterThan(0);
    });

    it('should throw LayoutError on invalid input', async () => {
      // 创建一个无效的 AST 对象
      const invalidAst = null as any;

      await expect(getLayoutedElements(invalidAst)).rejects.toThrow(LayoutError);
      await expect(getLayoutedElements(invalidAst)).rejects.toThrow('AST 对象不能为空');
    });

    it('should handle performance limits gracefully', async () => {
      // 这个测试模拟了一个可能产生大量节点的正则表达式
      const parseResult = parseRegex('a{1,10}b{1,10}c{1,10}');

      if (!parseResult.ast) {
        throw new Error(`解析失败: ${parseResult.error}`);
      }

      const result = await getLayoutedElements(parseResult.ast, {
        maxNodes: 10, // 设置较小的限制来测试
        logLevel: LogLevel.ERROR // 减少测试时的日志输出
      });

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
    });

    it('should use cache when enabled', async () => {
      const parseResult = parseRegex('abc');

      if (!parseResult.ast) {
        throw new Error(`解析失败: ${parseResult.error}`);
      }

      // 第一次调用
      const result1 = await getLayoutedElements(parseResult.ast, { enableCache: true });

      // 第二次调用应该使用缓存
      const result2 = await getLayoutedElements(parseResult.ast, { enableCache: true });

      expect(result1).toEqual(result2);
    });

    it('should handle custom configuration', async () => {
      const parseResult = parseRegex('(a|b)+');

      if (!parseResult.ast) {
        throw new Error(`解析失败: ${parseResult.error}`);
      }

      const result = await getLayoutedElements(parseResult.ast, {
        maxNodes: 50,
        maxRecursionDepth: 20,
        enableCache: false,
        logLevel: LogLevel.WARN
      });

      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(result.nodes.length).toBeGreaterThan(0);
    });

    it('should estimate node width based on content length', async () => {
      const parseResult = parseRegex('verylongregexpattern');
      if (!parseResult.ast) {
        throw new Error(`解析失败: ${parseResult.error}`);
      }

      const result = await getLayoutedElements(parseResult.ast);

      // 检查是否有节点
      if (result.nodes.length > 0) {
        const firstNode = result.nodes[0];
        // 宽度应该基于内容长度计算，至少为 80
        expect(firstNode.style?.width).toBeGreaterThanOrEqual(80);
      }
    });

    it('should handle empty regex gracefully', async () => {
      const parseResult = parseRegex('');
      if (!parseResult.ast) {
        // 空正则表达式可能解析失败，这是正常的
        expect(parseResult.error).toBeTruthy();
        return;
      }

      const result = await getLayoutedElements(parseResult.ast);

      // 应该返回有效的结构，即使是空的
      expect(result).toHaveProperty('nodes');
      expect(result).toHaveProperty('edges');
      expect(Array.isArray(result.nodes)).toBe(true);
      expect(Array.isArray(result.edges)).toBe(true);
    });
  });
});
