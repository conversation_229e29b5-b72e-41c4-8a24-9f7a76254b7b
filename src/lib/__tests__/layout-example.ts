/**
 * 这个文件包含一些示例，展示优化后的 layout.ts 的功能
 * 可以用于手动测试和验证
 */

import { getLayoutedElements, clearLayoutCache } from '../layout';
import { parseRegex } from '../parser';
import { LogLevel } from '../constants';

// 示例 1: 简单正则表达式
export async function testSimpleRegex() {
  console.log('=== 测试简单正则表达式 ===');
  const parseResult = parseRegex('abc');
  
  if (!parseResult.ast) {
    console.error('解析失败:', parseResult.error);
    return;
  }
  
  try {
    const result = await getLayoutedElements(parseResult.ast, {
      logLevel: LogLevel.INFO,
      enableCache: true
    });
    console.log('结果:', {
      nodeCount: result.nodes.length,
      edgeCount: result.edges.length,
      firstNode: result.nodes[0]
    });
  } catch (error) {
    console.error('布局计算失败:', error);
  }
}

// 示例 2: 空正则表达式
export async function testEmptyRegex() {
  console.log('=== 测试空正则表达式 ===');
  
  // 创建一个空的 AST 来模拟空正则表达式
  const emptyAst = {
    type: 'RegExp' as const,
    body: null as any,
    flags: ''
  };
  
  try {
    const result = await getLayoutedElements(emptyAst);
    console.log('空正则表达式结果:', {
      nodeCount: result.nodes.length,
      edgeCount: result.edges.length,
      placeholderNode: result.nodes[0]
    });
  } catch (error) {
    console.error('布局计算失败:', error);
  }
}

// 示例 3: 复杂正则表达式
export async function testComplexRegex() {
  console.log('=== 测试复杂正则表达式 ===');
  const parseResult = parseRegex('(a+|b*){2,5}[cde]?\\d+');
  
  if (!parseResult.ast) {
    console.error('解析失败:', parseResult.error);
    return;
  }
  
  try {
    const result = await getLayoutedElements(parseResult.ast);
    console.log('复杂正则表达式结果:', {
      nodeCount: result.nodes.length,
      edgeCount: result.edges.length,
      sampleNodes: result.nodes.slice(0, 3).map(n => ({
        id: n.id,
        label: n.data.content,
        position: n.position
      }))
    });
  } catch (error) {
    console.error('布局计算失败:', error);
  }
}

// 示例 4: 测试性能监控
export async function testPerformanceMonitoring() {
  console.log('=== 测试性能监控 ===');
  
  // 创建一个相对复杂的正则表达式来触发性能监控
  const complexRegex = 'a{1,3}b{1,3}c{1,3}d{1,3}e{1,3}f{1,3}';
  const parseResult = parseRegex(complexRegex);
  
  if (!parseResult.ast) {
    console.error('解析失败:', parseResult.error);
    return;
  }
  
  try {
    const result = await getLayoutedElements(parseResult.ast);
    console.log('性能监控测试结果:', {
      nodeCount: result.nodes.length,
      edgeCount: result.edges.length,
      performanceWarning: result.nodes.length > 160 ? '可能触发性能警告' : '性能正常'
    });
  } catch (error) {
    console.error('布局计算失败:', error);
  }
}

// 示例 5: 测试配置选项
export async function testConfigurationOptions() {
  console.log('=== 测试配置选项 ===');

  const parseResult = parseRegex('(a+|b*){2,5}');

  if (!parseResult.ast) {
    console.error('解析失败:', parseResult.error);
    return;
  }

  try {
    // 测试不同的配置
    const result1 = await getLayoutedElements(parseResult.ast, {
      maxNodes: 20,
      enableCache: false,
      logLevel: LogLevel.WARN
    });

    console.log('限制节点数配置结果:', {
      nodeCount: result1.nodes.length,
      edgeCount: result1.edges.length
    });

    // 测试缓存
    const result2 = await getLayoutedElements(parseResult.ast, {
      enableCache: true,
      logLevel: LogLevel.DEBUG
    });

    console.log('启用缓存配置结果:', {
      nodeCount: result2.nodes.length,
      edgeCount: result2.edges.length
    });

  } catch (error) {
    console.error('配置测试失败:', error);
  }
}

// 运行所有示例的函数
export async function runAllExamples() {
  await testSimpleRegex();
  console.log('');

  await testEmptyRegex();
  console.log('');

  await testComplexRegex();
  console.log('');

  await testPerformanceMonitoring();
  console.log('');

  await testConfigurationOptions();
  console.log('');

  // 清理缓存
  clearLayoutCache();
  console.log('缓存已清理');
}

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
  runAllExamples().catch(console.error);
}
