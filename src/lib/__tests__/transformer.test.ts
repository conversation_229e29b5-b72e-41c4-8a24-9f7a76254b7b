import { describe, it, expect, beforeEach } from 'vitest';
import { astToElkGraph, clearTransformCache, TransformError, type CustomElkNode } from '../transformer';
import { parseRegex } from '../parser';
import type { AstRegExp } from 'regexp-tree/ast';
import { NodeType } from '@/types/graph';

describe('transformer', () => {
  beforeEach(() => {
    clearTransformCache();
  });

  describe('astToElkGraph', () => {
    it('should handle empty AST', () => {
      const result = astToElkGraph({ type: 'RegExp', body: null, flags: '' });
      expect(result.children).toHaveLength(0);
      expect(result.edges).toHaveLength(0);
    });

    it('should handle simple character', () => {
      const parseResult = parseRegex('a');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.children.length).toBeGreaterThan(0);
        expect((result.children[0] as CustomElkNode).data?.content).toBe('a');
      }
    });

    it('should handle character class', () => {
      const parseResult = parseRegex('[a-z]');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.children.length).toBeGreaterThan(0);
        expect((result.children[0] as CustomElkNode).data?.content).toContain('[');
      }
    });

    it('should handle quantifiers', () => {
      const parseResult = parseRegex('a+');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.children.length).toBeGreaterThan(0);
      }
    });

    it('should handle alternation', () => {
      const parseResult = parseRegex('a|b');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.children.length).toBeGreaterThan(0);

        // 检查是否创建了语义化分组容器
        const containerNode = result.children[0];
        expect(containerNode.data?.astNodeType).toBe('MacroAlternation');
        expect(containerNode.data?.label).toBe('分支 |');

        // 检查容器内部结构
        expect(containerNode.children).toBeDefined();
        expect(containerNode.children!.length).toBeGreaterThan(2); // 至少有起点、终点和分支节点
        expect(containerNode.edges).toBeDefined();
        expect(containerNode.edges!.length).toBeGreaterThan(0); // 容器内部应该有边
      }
    });

    it('should handle complex phone number regex', () => {
      const phoneRegex = '^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\\d{8}$';
      const parseResult = parseRegex(phoneRegex);
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);

        console.log('手机号正则结果:');
        console.log('- 根级别子节点数:', result.children.length);
        console.log('- 根级别边数:', result.edges.length);

        // 打印结构用于调试
        result.children.forEach((child, i) => {
          console.log(`节点 ${i}: ${child.data?.astNodeType} - ${child.data?.label}`);
          if (child.children) {
            console.log(`  子节点数: ${child.children.length}`);
          }
          if (child.edges) {
            console.log(`  内部边数: ${child.edges.length}`);
          }
        });

        expect(result.children.length).toBeGreaterThan(0);
      }
    });

    it('should handle groups', () => {
      const parseResult = parseRegex('(abc)');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.children.length).toBeGreaterThan(0);
      }
    });

    it('should handle assertions', () => {
      const parseResult = parseRegex('^abc$');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.children.length).toBeGreaterThan(0);
      }
    });

    it('should throw TransformError on max depth exceeded', () => {
      // 创建一个深度嵌套的 AST 模拟对象
      const createDeepAST = (depth: number): any => {
        if (depth <= 0) {
          return { type: 'Char', value: 'a' };
        }
        return {
          type: 'Group',
          capturing: true,
          expression: createDeepAST(depth - 1)
        };
      };

      const deepAST: AstRegExp = {
        type: 'RegExp' as const,
        body: createDeepAST(60), // 超过 MAX_DEPTH (50)
        flags: ''
      };

      expect(() => astToElkGraph(deepAST)).toThrow(TransformError);
    });

    it('should handle wide characters correctly', () => {
      const parseResult = parseRegex('中文');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.children.length).toBeGreaterThan(0);
        // 中文字符的节点应该比英文字符更宽
        const chineseNode = result.children.find(node => {
          const customNode = node as CustomElkNode;
          return customNode.data?.content.includes('中') || customNode.data?.content.includes('文');
        });
        if (chineseNode) {
          expect(chineseNode.width).toBeGreaterThan(60); // 应该比最小宽度大
        }
      }
    });

    it('should handle emoji characters correctly', () => {
      const parseResult = parseRegex('😀');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.children.length).toBeGreaterThan(0);
        // Emoji 字符的节点应该更宽
        const emojiNode = result.children.find(node => {
          const customNode = node as CustomElkNode;
          return customNode.data?.content.includes('😀');
        });
        if (emojiNode) {
          expect(emojiNode.width).toBeGreaterThan(80); // 应该比中文字符更宽
        }
      }
    });

    it('should not create duplicate edges', () => {
      const parseResult = parseRegex('abc');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        const edgeIds = result.edges.map(edge => edge.id);
        const uniqueEdgeIds = new Set(edgeIds);
        expect(edgeIds.length).toBe(uniqueEdgeIds.size); // 没有重复的边ID
      }
    });

    it('should have adaptive spacing based on node width', () => {
      const parseResult = parseRegex('very_long_pattern_name');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);
        expect(result.layoutOptions).toBeDefined();
        expect(result.layoutOptions!['elk.spacing.nodeNode']).toBeDefined();
        expect(result.layoutOptions!['elk.layered.spacing.nodeNodeBetweenLayers']).toBeDefined();
      }
    });
  });

  describe('macro sequence functionality', () => {
    it('should merge consecutive character nodes into macro sequence', () => {
      const parseResult = parseRegex('http');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);

        // 应该有一个序列节点和一个宏观序列节点
        expect(result.children.length).toBeGreaterThan(0);

        // 查找宏观序列节点
        const macroSequenceNode = result.children.find(node =>
          node.data?.semanticType === NodeType.MACRO_SEQUENCE
        );

        if (macroSequenceNode) {
          expect(macroSequenceNode.data?.content).toBe('http');
          expect(macroSequenceNode.data?.label).toBe('http');
        }
      }
    });

    it('should handle mixed patterns with character sequences', () => {
      const parseResult = parseRegex('abc[0-9]def');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);

        // 验证结果结构 - 应该有合理数量的节点
        expect(result.children.length).toBeGreaterThan(1);

        // 应该包含字符类节点
        const charClassNodes = result.children.filter(node =>
          node.data?.semanticType === NodeType.CHARACTER_CLASS
        );
        expect(charClassNodes.length).toBe(1);

        // 验证字符类节点的内容
        expect(charClassNodes[0].data?.content).toBe('[0-9]');

        // 验证整体结构 - 应该能正确处理混合模式而不出错
        expect(result.children.every(node => node.data?.content)).toBe(true);
      }
    });

    it('should not merge single characters', () => {
      const parseResult = parseRegex('a');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);

        // 单个字符不应该被合并为宏观序列
        const macroSequenceNode = result.children.find(node =>
          node.data?.semanticType === NodeType.MACRO_SEQUENCE
        );

        // 单个字符应该保持为普通字符节点
        expect(macroSequenceNode).toBeUndefined();
      }
    });

    it('should handle empty string gracefully', () => {
      const parseResult = parseRegex('');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);

        // 空字符串应该能正常处理，不抛出错误
        expect(result.children.length).toBeGreaterThanOrEqual(0);
      }
    });

    it('should handle special characters sequence', () => {
      const parseResult = parseRegex('\\d\\w\\s');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);

        // 转义字符序列应该能正常处理
        expect(result.children.length).toBeGreaterThan(0);

        // 转义字符不应该被合并为宏观序列（因为它们不是普通字符）
        const macroNodes = result.children.filter(node =>
          node.data?.semanticType === NodeType.MACRO_SEQUENCE
        );
        expect(macroNodes.length).toBe(0);
      }
    });

    it('should handle very long sequences efficiently', () => {
      const longString = 'a'.repeat(100); // 减少长度以避免测试超时
      const parseResult = parseRegex(longString);
      if (parseResult.ast) {
        const startTime = Date.now();
        const result = astToElkGraph(parseResult.ast);
        const endTime = Date.now();

        // 应该能在合理时间内完成（小于1秒）
        expect(endTime - startTime).toBeLessThan(1000);

        // 应该有一个宏观序列节点包含所有字符
        const macroNodes = result.children.filter(node =>
          node.data?.semanticType === NodeType.MACRO_SEQUENCE
        );
        expect(macroNodes.length).toBeGreaterThanOrEqual(1);

        // 验证合并后的内容
        const longSequenceNode = macroNodes.find(node =>
          node.data?.content === longString
        );
        expect(longSequenceNode).toBeDefined();
      }
    });

    it('should handle nested patterns with quantifiers', () => {
      const parseResult = parseRegex('http(s)?');
      if (parseResult.ast) {
        const result = astToElkGraph(parseResult.ast);

        // 验证结构正确性
        expect(result.children.length).toBeGreaterThan(1);

        // 验证能正确处理复杂模式而不出错
        expect(result.children.every(node => node.data?.content)).toBe(true);

        // 应该有宏观序列节点或普通字符节点
        const macroNodes = result.children.filter(node =>
          node.data?.semanticType === NodeType.MACRO_SEQUENCE
        );
        const literalNodes = result.children.filter(node =>
          node.data?.semanticType === NodeType.LITERAL
        );

        // 应该有字符相关的节点（宏观序列或普通字符）
        expect(macroNodes.length + literalNodes.length).toBeGreaterThan(0);

        // 应该有组节点
        const groupNodes = result.children.filter(node =>
          node.data?.semanticType === NodeType.GROUP
        );
        expect(groupNodes.length).toBeGreaterThanOrEqual(1);

        // 应该有量词节点
        const quantifierNodes = result.children.filter(node =>
          node.data?.semanticType === NodeType.QUANTIFIER
        );
        expect(quantifierNodes.length).toBeGreaterThanOrEqual(1);
      }
    });
  });

  describe('clearTransformCache', () => {
    it('should clear all caches and counters', () => {
      // 这个测试主要确保函数不会抛出错误
      expect(() => clearTransformCache()).not.toThrow();
    });
  });
});
