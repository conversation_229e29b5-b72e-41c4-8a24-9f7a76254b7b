/**
 * API 高级测试 - 并发、压力测试和健康检查
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { cleanup } from '../app/api/explain/route';

// Mock fetch for testing
const mockFetch = vi.fn();
global.fetch = mockFetch as any;

describe('API Advanced Tests', () => {
  beforeEach(() => {
    mockFetch.mockReset();
  });

  afterEach(() => {
    cleanup();
  });

  describe('Concurrent Request Handling', () => {
    it('should handle multiple concurrent requests efficiently', async () => {
      // 模拟成功的 API 响应
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          explanation: '这是一个并发测试解释'
        })
      });

      const { useRegexStore } = await import('../store/regex.store');
      const store = useRegexStore.getState();

      // 清理状态
      store.aiExplanations.clear();
      store.pendingExplanations.clear();

      // 创建多个并发请求
      const concurrentRequests = Array.from({ length: 10 }, (_, i) => 
        store._fetchExplanationForNode(`node-${i}`, `\\d{${i + 1}}`)
      );

      // 等待所有请求完成
      await Promise.all(concurrentRequests);

      // 等待状态更新
      await new Promise(resolve => setTimeout(resolve, 100));

      // 验证所有请求都被处理
      const updatedStore = useRegexStore.getState();
      expect(updatedStore.aiExplanations.size).toBeGreaterThan(0);
      
      // 验证没有重复的 fetch 调用（相同内容应该被去重）
      expect(mockFetch).toHaveBeenCalled();
    });

    it('should handle concurrent requests for same content correctly', async () => {
      let resolveCount = 0;
      mockFetch.mockImplementation(() => {
        resolveCount++;
        return Promise.resolve({
          ok: true,
          json: async () => ({
            explanation: `解释 ${resolveCount}`
          })
        });
      });

      const { useRegexStore } = await import('../store/regex.store');
      const store = useRegexStore.getState();

      // 清理状态
      store.aiExplanations.clear();
      store.pendingExplanations.clear();

      const sameContent = '\\w+';
      
      // 同时发起多个相同内容的请求
      const sameContentRequests = Array.from({ length: 5 }, (_, i) => 
        store._fetchExplanationForNode(`node-same-${i}`, sameContent)
      );

      await Promise.all(sameContentRequests);
      await new Promise(resolve => setTimeout(resolve, 100));

      // 验证相同内容只被请求一次
      expect(mockFetch).toHaveBeenCalledTimes(1);
      
      const updatedStore = useRegexStore.getState();
      expect(updatedStore.aiExplanations.has(sameContent)).toBe(true);
    });
  });

  describe('Stress Testing', () => {
    it('should handle rapid sequential requests', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          explanation: '快速请求测试'
        })
      });

      const { useRegexStore } = await import('../store/regex.store');
      const store = useRegexStore.getState();

      store.aiExplanations.clear();
      store.pendingExplanations.clear();

      const startTime = Date.now();
      
      // 快速连续发送请求
      for (let i = 0; i < 20; i++) {
        store._fetchExplanationForNode(`rapid-${i}`, `pattern-${i}`);
        // 很短的延迟模拟快速用户操作
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // 验证处理时间合理（应该在合理范围内）
      expect(duration).toBeLessThan(5000); // 5秒内完成

      // 等待所有异步操作完成
      await new Promise(resolve => setTimeout(resolve, 500));

      expect(mockFetch).toHaveBeenCalled();
    });

    it('should maintain performance under load', async () => {
      const responseTime = 50; // 模拟 50ms 响应时间
      
      mockFetch.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: async () => ({
              explanation: '负载测试解释'
            })
          }), responseTime)
        )
      );

      const { useRegexStore } = await import('../store/regex.store');
      const store = useRegexStore.getState();

      store.aiExplanations.clear();
      store.pendingExplanations.clear();

      const batchSize = 15;
      const batches = 3;
      
      for (let batch = 0; batch < batches; batch++) {
        const batchPromises = Array.from({ length: batchSize }, (_, i) => 
          store._fetchExplanationForNode(
            `load-${batch}-${i}`, 
            `pattern-${batch}-${i}`
          )
        );

        await Promise.all(batchPromises);
        
        // 批次间短暂休息
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 验证系统仍然响应
      expect(mockFetch).toHaveBeenCalled();
      
      const finalStore = useRegexStore.getState();
      expect(finalStore.aiExplanations.size).toBeGreaterThan(0);
    });
  });

  describe('Error Recovery Under Load', () => {
    it('should handle mixed success/failure scenarios', async () => {
      let callCount = 0;
      mockFetch.mockImplementation(() => {
        callCount++;
        // 模拟 30% 失败率
        if (callCount % 3 === 0) {
          return Promise.resolve({
            ok: false,
            status: 500,
            statusText: 'Internal Server Error'
          });
        }
        
        return Promise.resolve({
          ok: true,
          json: async () => ({
            explanation: `成功响应 ${callCount}`
          })
        });
      });

      const { useRegexStore } = await import('../store/regex.store');
      const store = useRegexStore.getState();

      store.aiExplanations.clear();
      store.pendingExplanations.clear();

      // 发送混合请求
      const mixedRequests = Array.from({ length: 12 }, (_, i) => 
        store._fetchExplanationForNode(`mixed-${i}`, `pattern-${i}`)
      );

      await Promise.all(mixedRequests);
      await new Promise(resolve => setTimeout(resolve, 200));

      // 验证系统处理了成功和失败的情况
      expect(mockFetch).toHaveBeenCalledTimes(12);

      const finalStore = useRegexStore.getState();
      // 由于去重机制，实际存储的数量可能少于请求数量
      expect(finalStore.aiExplanations.size).toBeGreaterThan(0);
      expect(finalStore.aiExplanations.size).toBeLessThanOrEqual(12);
    });
  });

  describe('Memory and Resource Management', () => {
    it('should not leak memory during extended operation', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          explanation: '内存测试'
        })
      });

      const { useRegexStore } = await import('../store/regex.store');
      const store = useRegexStore.getState();

      const initialSize = store.aiExplanations.size;

      // 模拟长时间运行
      for (let cycle = 0; cycle < 5; cycle++) {
        // 添加一些数据
        for (let i = 0; i < 10; i++) {
          await store._fetchExplanationForNode(
            `memory-${cycle}-${i}`, 
            `pattern-${cycle}-${i}`
          );
        }

        // 清理部分数据（模拟实际使用中的清理）
        if (cycle > 0) {
          store.aiExplanations.clear();
        }

        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // 验证最终状态合理
      const finalSize = store.aiExplanations.size;
      expect(finalSize).toBeLessThan(100); // 不应该无限增长
    });
  });
});
