/**
 * GraphPanel 事件流防抖逻辑测试
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import GraphPanel from '../../components/panels/GraphPanel';
import { useRegexStore } from '../store/regex.store';

// Mock useRegexStore
vi.mock('../store/regex.store', () => ({
  useRegexStore: vi.fn()
}));

// Mock ReactFlow - 提供默认和无效节点数据的 mock
const createReactFlowMock = (nodeData: Record<string, unknown> = { label: '[a-z]+' }) => ({
  ReactFlow: ({ onNodeMouseEnter, onNodeMouseLeave, children }: {
    onNodeMouseEnter?: (event: unknown, node: unknown) => void;
    onNodeMouseLeave?: (event: unknown, node: unknown) => void;
    children?: React.ReactNode;
  }) => (
    <div data-testid="react-flow">
      <div
        data-testid="test-node"
        onMouseEnter={(e) => onNodeMouseEnter?.(e, {
          id: 'test-node-1',
          data: nodeData
        })}
        onMouseLeave={(e) => onNodeMouseLeave?.(e, {
          id: 'test-node-1',
          data: nodeData
        })}
      >
        Test Node
      </div>
      {children}
    </div>
  ),
  Background: () => <div data-testid="background" />,
  BackgroundVariant: { dots: 'dots' }
});

vi.mock('@xyflow/react', () => createReactFlowMock());

describe('GraphPanel 防抖逻辑', () => {
  const mockSetHoveredElementId = vi.fn();
  const mockFetchExplanationForNode = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    (useRegexStore as unknown as ReturnType<typeof vi.fn>).mockImplementation((selector: (state: unknown) => unknown) => {
      if (typeof selector === 'function') {
        const mockState = {
          setHoveredElementId: mockSetHoveredElementId,
          _fetchExplanationForNode: mockFetchExplanationForNode
        };
        return selector(mockState);
      }
      return {
        setHoveredElementId: mockSetHoveredElementId,
        _fetchExplanationForNode: mockFetchExplanationForNode
      };
    });
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('应该立即调用 setHoveredElementId', async () => {
    const { getByTestId } = render(<GraphPanel />);
    const testNode = getByTestId('test-node');

    fireEvent.mouseEnter(testNode);

    // setHoveredElementId 应该立即被调用
    expect(mockSetHoveredElementId).toHaveBeenCalledWith('test-node-1');
    expect(mockSetHoveredElementId).toHaveBeenCalledTimes(1);
  });

  it('应该防抖调用 _fetchExplanationForNode', async () => {
    const { getByTestId } = render(<GraphPanel />);
    const testNode = getByTestId('test-node');

    fireEvent.mouseEnter(testNode);

    // _fetchExplanationForNode 不应该立即被调用
    expect(mockFetchExplanationForNode).not.toHaveBeenCalled();

    // 等待防抖延迟（150ms）
    vi.advanceTimersByTime(150);

    // 现在应该被调用
    expect(mockFetchExplanationForNode).toHaveBeenCalledWith('test-node-1', '[a-z]+');
    expect(mockFetchExplanationForNode).toHaveBeenCalledTimes(1);
  });

  it('应该在快速悬停时取消之前的防抖调用', async () => {
    const { getByTestId } = render(<GraphPanel />);
    const testNode = getByTestId('test-node');

    // 第一次悬停
    fireEvent.mouseEnter(testNode);
    
    // 等待 100ms（小于防抖延迟）
    vi.advanceTimersByTime(100);
    
    // 第二次悬停（应该取消第一次的防抖调用）
    fireEvent.mouseEnter(testNode);
    
    // 等待防抖延迟
    vi.advanceTimersByTime(150);

    // _fetchExplanationForNode 应该只被调用一次（第二次悬停的）
    expect(mockFetchExplanationForNode).toHaveBeenCalledTimes(1);
    expect(mockFetchExplanationForNode).toHaveBeenCalledWith('test-node-1', '[a-z]+');
  });

  it('应该在鼠标离开时取消防抖调用', async () => {
    const { getByTestId } = render(<GraphPanel />);
    const testNode = getByTestId('test-node');

    // 鼠标进入
    fireEvent.mouseEnter(testNode);
    
    // 等待 100ms（小于防抖延迟）
    vi.advanceTimersByTime(100);
    
    // 鼠标离开
    fireEvent.mouseLeave(testNode);
    
    // 等待防抖延迟
    vi.advanceTimersByTime(150);

    // _fetchExplanationForNode 不应该被调用
    expect(mockFetchExplanationForNode).not.toHaveBeenCalled();
    
    // setHoveredElementId 应该被调用两次：进入时设置 id，离开时设置 null
    expect(mockSetHoveredElementId).toHaveBeenCalledTimes(2);
    expect(mockSetHoveredElementId).toHaveBeenNthCalledWith(1, 'test-node-1');
    expect(mockSetHoveredElementId).toHaveBeenNthCalledWith(2, null);
  });

  it('应该处理空字符串标签的节点数据', async () => {
    // 测试空字符串或空白字符串的情况
    const { getByTestId } = render(<GraphPanel />);
    const testNode = getByTestId('test-node');

    // 模拟节点数据包含空字符串标签的情况
    // 这里我们通过修改 mock 的返回值来测试
    const originalMock = (useRegexStore as unknown as { mock: unknown }).mock;
    (useRegexStore as unknown as ReturnType<typeof vi.fn>).mockImplementation((selector: (state: unknown) => unknown) => {
      if (typeof selector === 'function') {
        const mockState = {
          setHoveredElementId: mockSetHoveredElementId,
          _fetchExplanationForNode: mockFetchExplanationForNode
        };
        return selector(mockState);
      }
      return {
        setHoveredElementId: mockSetHoveredElementId,
        _fetchExplanationForNode: mockFetchExplanationForNode
      };
    });

    fireEvent.mouseEnter(testNode);

    // setHoveredElementId 应该被调用
    expect(mockSetHoveredElementId).toHaveBeenCalledWith('test-node-1');

    // 等待防抖延迟
    vi.advanceTimersByTime(150);

    // 对于有效的标签，_fetchExplanationForNode 应该被调用
    expect(mockFetchExplanationForNode).toHaveBeenCalledWith('test-node-1', '[a-z]+');

    // 恢复原始 mock
    (useRegexStore as unknown as { mock: unknown }).mock = originalMock;
  });
});
