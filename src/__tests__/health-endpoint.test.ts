/**
 * 健康检查端点测试
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GET, HEAD } from '../app/api/health/route';
import type { HealthResponse } from '../app/api/health/route';

describe('Health Endpoint Tests', () => {
  const originalEnv = { ...process.env };

  beforeEach(() => {
    // 重置环境变量
    Object.keys(process.env).forEach(key => {
      if (!originalEnv.hasOwnProperty(key)) {
        delete process.env[key];
      }
    });
    Object.assign(process.env, originalEnv);
  });

  afterEach(() => {
    // 恢复环境变量
    Object.keys(process.env).forEach(key => {
      if (!originalEnv.hasOwnProperty(key)) {
        delete process.env[key];
      }
    });
    Object.assign(process.env, originalEnv);
  });

  describe('GET /api/health', () => {
    it('should return healthy status when properly configured', async () => {
      // 设置必要的环境变量
      process.env.OPENROUTER_API_KEY = 'test-key';
      process.env.NODE_ENV = 'test';
      process.env.npm_package_version = '1.0.0';

      const response = await GET();
      const data = await response.json() as HealthResponse;

      expect(response.status).toBe(200);
      expect(data.status).toBe('healthy');
      expect(data.services.openrouter).toBe('configured');
      expect(data.environment).toBe('test');
      expect(data.version).toBe('1.0.0');
      expect(typeof data.uptime).toBe('number');
      expect(data.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    });

    it('should return unhealthy status when not configured', async () => {
      // 移除关键环境变量
      delete process.env.OPENROUTER_API_KEY;
      process.env.NODE_ENV = 'test';

      const response = await GET();
      const data = await response.json() as HealthResponse;

      expect(response.status).toBe(503);
      expect(data.status).toBe('unhealthy');
      expect(data.services.openrouter).toBe('not_configured');
      expect(data.environment).toBe('test');
    });

    it('should include proper cache headers', async () => {
      process.env.OPENROUTER_API_KEY = 'test-key';

      const response = await GET();

      expect(response.headers.get('Cache-Control')).toBe('no-cache, no-store, must-revalidate');
      expect(response.headers.get('Pragma')).toBe('no-cache');
      expect(response.headers.get('Expires')).toBe('0');
    });

    it('should handle missing version gracefully', async () => {
      process.env.OPENROUTER_API_KEY = 'test-key';
      delete process.env.npm_package_version;

      const response = await GET();
      const data = await response.json() as HealthResponse;

      expect(data.version).toBe('0.1.0'); // 默认版本
    });

    it('should handle unknown environment', async () => {
      process.env.OPENROUTER_API_KEY = 'test-key';
      delete process.env.NODE_ENV;

      const response = await GET();
      const data = await response.json() as HealthResponse;

      expect(data.environment).toBe('unknown');
    });
  });

  describe('HEAD /api/health', () => {
    it('should return 200 when healthy', async () => {
      process.env.OPENROUTER_API_KEY = 'test-key';

      const response = await HEAD();

      expect(response.status).toBe(200);
      expect(response.headers.get('Cache-Control')).toBe('no-cache, no-store, must-revalidate');
      
      // HEAD 请求不应该有响应体
      const text = await response.text();
      expect(text).toBe('');
    });

    it('should return 503 when unhealthy', async () => {
      delete process.env.OPENROUTER_API_KEY;

      const response = await HEAD();

      expect(response.status).toBe(503);
      
      const text = await response.text();
      expect(text).toBe('');
    });
  });

  describe('Error Handling', () => {
    it('should handle exceptions gracefully', async () => {
      // 模拟 process.uptime 抛出错误
      const originalUptime = process.uptime;
      process.uptime = vi.fn().mockImplementation(() => {
        throw new Error('Uptime error');
      });

      try {
        const response = await GET();
        const data = await response.json() as HealthResponse;

        expect(response.status).toBe(503);
        expect(data.status).toBe('unhealthy');
        expect(data.uptime).toBe(0);
        expect(data.version).toBe('unknown');
      } finally {
        process.uptime = originalUptime;
      }
    });

    it('should handle HEAD request exceptions', async () => {
      // 模拟异常情况 - 删除关键环境变量并模拟错误
      const originalKey = process.env.OPENROUTER_API_KEY;
      delete process.env.OPENROUTER_API_KEY;

      try {
        const response = await HEAD();
        expect(response.status).toBe(503);
      } finally {
        if (originalKey) {
          process.env.OPENROUTER_API_KEY = originalKey;
        }
      }
    });
  });

  describe('Response Format Validation', () => {
    it('should return valid HealthResponse format', async () => {
      process.env.OPENROUTER_API_KEY = 'test-key';
      process.env.NODE_ENV = 'test';

      const response = await GET();
      const data = await response.json() as HealthResponse;

      // 验证必需字段
      expect(data).toHaveProperty('status');
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('uptime');
      expect(data).toHaveProperty('version');
      expect(data).toHaveProperty('services');
      expect(data).toHaveProperty('environment');

      // 验证 services 对象结构
      expect(data.services).toHaveProperty('openrouter');
      expect(data.services).toHaveProperty('database');
      expect(data.services.database).toBe('not_applicable');

      // 验证状态值
      expect(['healthy', 'unhealthy']).toContain(data.status);
      expect(['configured', 'not_configured']).toContain(data.services.openrouter);
    });
  });

  describe('Performance', () => {
    it('should respond quickly', async () => {
      process.env.OPENROUTER_API_KEY = 'test-key';

      const startTime = Date.now();
      await GET();
      const duration = Date.now() - startTime;

      // 健康检查应该很快（小于 100ms）
      expect(duration).toBeLessThan(100);
    });

    it('should handle multiple concurrent health checks', async () => {
      process.env.OPENROUTER_API_KEY = 'test-key';

      const concurrentChecks = Array.from({ length: 10 }, () => GET());
      
      const startTime = Date.now();
      const responses = await Promise.all(concurrentChecks);
      const duration = Date.now() - startTime;

      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // 并发处理应该高效
      expect(duration).toBeLessThan(500);
    });
  });
});
