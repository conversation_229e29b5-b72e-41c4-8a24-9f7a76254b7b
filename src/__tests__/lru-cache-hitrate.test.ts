/**
 * LRUCache 命中率统计测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { LRUCache } from '../lib/lru-cache';

describe('LRUCache 命中率统计', () => {
  let cache: LRUCache<string, string>;

  beforeEach(() => {
    cache = new LRUCache<string, string>({ maxSize: 3 });
  });

  it('应该正确统计命中率 - 无请求时', () => {
    const stats = cache.getStats();
    expect(stats.hitCount).toBe(0);
    expect(stats.requestCount).toBe(0);
    expect(stats.hitRate).toBeUndefined();
  });

  it('应该正确统计命中率 - 全部命中', () => {
    // 设置缓存
    cache.set('key1', 'value1');
    cache.set('key2', 'value2');

    // 全部命中
    cache.get('key1');
    cache.get('key2');
    cache.get('key1');

    const stats = cache.getStats();
    expect(stats.hitCount).toBe(3);
    expect(stats.requestCount).toBe(3);
    expect(stats.hitRate).toBe(100);
  });

  it('应该正确统计命中率 - 部分命中', () => {
    // 设置缓存
    cache.set('key1', 'value1');

    // 1次命中，2次未命中
    cache.get('key1'); // 命中
    cache.get('key2'); // 未命中
    cache.get('key3'); // 未命中

    const stats = cache.getStats();
    expect(stats.hitCount).toBe(1);
    expect(stats.requestCount).toBe(3);
    expect(stats.hitRate).toBeCloseTo(33.33, 2); // 使用toBeCloseTo避免浮点精度问题
  });

  it('应该正确统计命中率 - TTL 过期', () => {
    vi.useFakeTimers();

    const cacheWithTTL = new LRUCache<string, string>({ maxSize: 3, ttl: 10 });

    // 设置缓存
    cacheWithTTL.set('key1', 'value1');

    // 立即获取（命中）
    cacheWithTTL.get('key1');

    // 等待过期后获取（未命中）
    vi.advanceTimersByTime(20); // 超过 TTL 时间
    cacheWithTTL.get('key1');

    const stats = cacheWithTTL.getStats();
    expect(stats.hitCount).toBe(1);
    expect(stats.requestCount).toBe(2);
    expect(stats.hitRate).toBe(50);

    vi.useRealTimers();
  });

  it('应该在 clear 时重置统计', () => {
    // 设置缓存并产生一些统计
    cache.set('key1', 'value1');
    cache.get('key1');
    cache.get('key2'); // 未命中

    let stats = cache.getStats();
    expect(stats.hitCount).toBe(1);
    expect(stats.requestCount).toBe(2);

    // 清空缓存
    cache.clear();

    stats = cache.getStats();
    expect(stats.hitCount).toBe(0);
    expect(stats.requestCount).toBe(0);
    expect(stats.hitRate).toBeUndefined();
  });

  it('应该在 clone 时复制统计信息', () => {
    // 设置缓存并产生一些统计
    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    cache.get('key1'); // 命中
    cache.get('key3'); // 未命中

    const originalStats = cache.getStats();
    expect(originalStats.hitCount).toBe(1);
    expect(originalStats.requestCount).toBe(2);

    // 克隆缓存
    const clonedCache = cache.clone();
    const clonedStats = clonedCache.getStats();

    // 统计信息应该被复制
    expect(clonedStats.hitCount).toBe(originalStats.hitCount);
    expect(clonedStats.requestCount).toBe(originalStats.requestCount);
    expect(clonedStats.hitRate).toBe(originalStats.hitRate);

    // 克隆后的缓存应该独立统计
    clonedCache.get('key1'); // 命中
    const newClonedStats = clonedCache.getStats();
    expect(newClonedStats.hitCount).toBe(2);
    expect(newClonedStats.requestCount).toBe(3);

    // 原缓存统计不应该受影响
    const newOriginalStats = cache.getStats();
    expect(newOriginalStats.hitCount).toBe(1);
    expect(newOriginalStats.requestCount).toBe(2);
  });
});
