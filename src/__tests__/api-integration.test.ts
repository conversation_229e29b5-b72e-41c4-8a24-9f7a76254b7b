/**
 * API 集成测试
 * 5.2 在 Vitest/Jest 环境为 fetch 提供 mock 或 polyfill，避免浏览器依赖
 */

import { describe, it, expect, vi, beforeEach, afterAll } from 'vitest';
import { useRegexStore } from '../store/regex.store';

// 保存原始fetch以便恢复
const originalFetch = global.fetch;

// Mock fetch for testing
const mockFetch = vi.fn();
// @ts-ignore - 7. 修复：显式断言写入 global.fetch
global.fetch = mockFetch;

describe('API Integration Tests', () => {
  beforeEach(() => {
    // 重置 mock
    mockFetch.mockReset();
    
    // 重置 store 状态
    const store = useRegexStore.getState();
    store.aiExplanations.clear();
    store.pendingExplanations.clear();
    store.pendingNodeIds.clear();
    store.aiErrorNodeIds.clear();
  });

  it('should successfully fetch explanation for node', async () => {
    // 模拟成功的 API 响应
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        explanation: '这是对 \\d+ 的模拟解释'
      })
    });

    const store = useRegexStore.getState();
    const nodeId = 'test-node-1';
    const nodeContent = '\\d+';

    // 调用函数
    await store._fetchExplanationForNode(nodeId, nodeContent);

    // 验证 fetch 被正确调用
    expect(mockFetch).toHaveBeenCalledWith('/api/explain', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ snippet: nodeContent }),
    });

    // 4. 优化：等待异步状态更新，保持与正式组件一致的使用姿势
    await new Promise(resolve => setTimeout(resolve, 0)); // 等待下一个事件循环

    // 验证结果被正确存储
    const updatedStore = useRegexStore.getState();
    expect(updatedStore.aiExplanations.has(nodeContent)).toBe(true);
    expect(updatedStore.aiExplanations.get(nodeContent)).toBe('这是对 \\d+ 的模拟解释');
  });

  it('should handle API errors gracefully', async () => {
    // 模拟 API 错误
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error'
    });

    const store = useRegexStore.getState();
    const nodeId = 'test-node-error';
    const nodeContent = '\\w+';

    // 调用函数
    await store._fetchExplanationForNode(nodeId, nodeContent);

    // 4. 优化：等待异步状态更新
    await new Promise(resolve => setTimeout(resolve, 0));

    // 验证错误被正确处理
    const updatedStore = useRegexStore.getState();
    expect(updatedStore.aiExplanations.has(nodeContent)).toBe(true);
    // 应该包含错误消息（使用中文错误消息）
    const errorMessage = updatedStore.aiExplanations.get(nodeContent);
    expect(errorMessage).toContain('解释获取失败');
  });

  it('should prevent duplicate requests for same content', async () => {
    // 模拟成功的 API 响应
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        explanation: '这是对 [a-z] 的模拟解释'
      })
    });

    const store = useRegexStore.getState();
    const nodeContent = '[a-z]';

    // 第一次调用
    await store._fetchExplanationForNode('node-1', nodeContent);
    
    // 第二次调用相同内容
    await store._fetchExplanationForNode('node-2', nodeContent);

    // 4. 优化：等待异步状态更新
    await new Promise(resolve => setTimeout(resolve, 0));

    // 验证 fetch 只被调用一次
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // 7. 简化：只验证基于内容的缓存，不依赖双键缓存逻辑
    const updatedStore = useRegexStore.getState();
    expect(updatedStore.aiExplanations.has(nodeContent)).toBe(true);
    expect(updatedStore.aiExplanations.get(nodeContent)).toBe('这是对 [a-z] 的模拟解释');
  });

  it('should handle concurrent requests properly', async () => {
    // 模拟延迟的 API 响应
    mockFetch.mockImplementationOnce(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({
            explanation: '这是对 \\s+ 的模拟解释'
          })
        }), 100)
      )
    );

    const store = useRegexStore.getState();
    const nodeContent = '\\s+';

    // 同时发起多个请求
    const promises = [
      store._fetchExplanationForNode('node-1', nodeContent),
      store._fetchExplanationForNode('node-2', nodeContent),
      store._fetchExplanationForNode('node-3', nodeContent)
    ];

    await Promise.all(promises);

    // 4. 优化：等待异步状态更新
    await new Promise(resolve => setTimeout(resolve, 0));

    // 验证 fetch 只被调用一次（并发去重）
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // 7. 简化：只验证基于内容的缓存，不依赖双键缓存逻辑
    const updatedStore = useRegexStore.getState();
    expect(updatedStore.aiExplanations.has(nodeContent)).toBe(true);
    expect(updatedStore.aiExplanations.get(nodeContent)).toBe('这是对 \\s+ 的模拟解释');
  });

  // 恢复原始fetch，防止影响其他测试套件
  afterAll(() => {
    global.fetch = originalFetch;
  });
});
