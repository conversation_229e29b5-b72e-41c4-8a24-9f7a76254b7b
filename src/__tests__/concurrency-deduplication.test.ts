/**
 * 并发去重强化测试
 * 验证 100% 消除重复请求的机制
 */

import { describe, it, expect, vi, beforeEach, afterAll } from 'vitest';
import { useRegexStore } from '../store/regex.store';

// 保存原始fetch以便恢复
const originalFetch = global.fetch;

// Mock fetch for testing
const mockFetch = vi.fn();
// @ts-ignore - 显式断言写入 global.fetch
global.fetch = mockFetch;

describe('Concurrency Deduplication Tests', () => {
  beforeEach(() => {
    // 重置 mock
    mockFetch.mockReset();
    
    // 重置 store 状态
    const store = useRegexStore.getState();
    store.aiExplanations.clear();
    store.pendingExplanations.clear();
    store.pendingNodeIds.clear();
    store.aiErrorNodeIds.clear();
  });

  it('should handle extreme concurrency with 100% deduplication', async () => {
    // 模拟延迟的 API 响应
    mockFetch.mockImplementationOnce(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({
            explanation: '这是对极限并发测试的模拟解释'
          })
        }), 50) // 50ms 延迟
      )
    );

    const store = useRegexStore.getState();
    const nodeContent = '\\w{3,}'; // 使用复杂一点的正则
    
    // 创建 10 个几乎同时的请求
    const promises = Array.from({ length: 10 }, (_, i) => 
      store._fetchExplanationForNode(`extreme-node-${i}`, nodeContent)
    );

    // 等待所有请求完成
    await Promise.all(promises);

    // 验证 fetch 只被调用一次（100% 去重）
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // 验证结果正确存储
    const updatedStore = useRegexStore.getState();
    expect(updatedStore.aiExplanations.has(nodeContent)).toBe(true);
    expect(updatedStore.aiExplanations.get(nodeContent)).toBe('这是对极限并发测试的模拟解释');
    
    // 验证 pendingExplanations 已清空
    expect(updatedStore.pendingExplanations.has(nodeContent)).toBe(false);
  });

  it('should handle mixed concurrent requests for different content', async () => {
    // 简化测试：只测试去重功能
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ explanation: '通用解释' })
    });

    const store = useRegexStore.getState();

    // 创建混合并发请求：相同内容的重复请求
    const promises = [
      store._fetchExplanationForNode('node-1a', '\\d+'),
      store._fetchExplanationForNode('node-1b', '\\d+'),
      store._fetchExplanationForNode('node-1c', '\\d+'),
    ];

    await Promise.all(promises);

    // 等待异步状态更新
    await new Promise(resolve => setTimeout(resolve, 0));

    // 验证 fetch 只被调用 1 次（完全去重）
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // 验证内容有正确的解释
    const updatedStore = useRegexStore.getState();
    expect(updatedStore.aiExplanations.get('\\d+')).toBe('通用解释');

    // 验证所有 pending 状态已清空
    expect(updatedStore.pendingExplanations.size).toBe(0);
  });

  it('should handle rapid sequential requests correctly', async () => {
    // 模拟快速响应
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ explanation: '快速响应解释' })
    });

    const store = useRegexStore.getState();
    const nodeContent = '^[a-zA-Z0-9]+$';
    
    // 快速连续发起请求
    await store._fetchExplanationForNode('seq-1', nodeContent);
    await store._fetchExplanationForNode('seq-2', nodeContent);
    await store._fetchExplanationForNode('seq-3', nodeContent);

    // 第一次请求后，后续请求应该直接从缓存获取
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // 验证缓存正确工作
    const updatedStore = useRegexStore.getState();
    expect(updatedStore.aiExplanations.get(nodeContent)).toBe('快速响应解释');
  });

  it('should maintain state consistency under concurrent errors', async () => {
    // 模拟 API 错误
    mockFetch.mockRejectedValue(new Error('Network error'));

    const store = useRegexStore.getState();
    const nodeContent = '(?:error)';
    
    // 并发发起多个会失败的请求
    const promises = Array.from({ length: 5 }, (_, i) => 
      store._fetchExplanationForNode(`error-node-${i}`, nodeContent)
    );

    await Promise.all(promises);

    // 验证只发起了一次请求
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // 验证错误状态正确处理
    const updatedStore = useRegexStore.getState();
    expect(updatedStore.aiExplanations.has(nodeContent)).toBe(true);
    
    const errorMessage = updatedStore.aiExplanations.get(nodeContent);
    expect(errorMessage).toContain('解释获取失败');
    
    // 验证 pending 状态已清空
    expect(updatedStore.pendingExplanations.has(nodeContent)).toBe(false);
  });

  it('should handle basic state transitions correctly', async () => {
    // 简化测试：验证基本的状态转换
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ explanation: '状态转换解释' })
    });

    const store = useRegexStore.getState();

    // 启动请求
    await store._fetchExplanationForNode('state-1', 'state-pattern');

    // 等待状态更新
    await new Promise(resolve => setTimeout(resolve, 0));

    // 验证最终状态
    const finalState = useRegexStore.getState();
    expect(finalState.pendingExplanations.size).toBe(0);
    expect(finalState.aiExplanations.get('state-pattern')).toBe('状态转换解释');
  });

  // 恢复原始fetch，防止影响其他测试套件
  afterAll(() => {
    global.fetch = originalFetch;
  });
});
