import { parseRegex } from './src/lib/parser.js';
import { astToElkGraph } from './src/lib/transformer.js';

// 测试手机号正则表达式
const phoneRegex = '/^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\\d{8}$/';

console.log('测试手机号正则:', phoneRegex);
console.log('='.repeat(80));

const parseResult = parseRegex(phoneRegex);
if (parseResult.ast) {
  const result = astToElkGraph(parseResult.ast);
  
  console.log('根级别结果:');
  console.log('- 子节点数量:', result.children.length);
  console.log('- 边数量:', result.edges.length);
  console.log();
  
  // 递归打印节点结构
  function printNodeStructure(nodes: any[], level = 0) {
    const indent = '  '.repeat(level);
    
    nodes.forEach((node, i) => {
      console.log(`${indent}${i}: ${node.id}`);
      console.log(`${indent}   类型: ${node.data?.astNodeType}`);
      console.log(`${indent}   标签: ${node.data?.label}`);
      console.log(`${indent}   语义类型: ${node.data?.semanticType}`);
      
      if (node.children && node.children.length > 0) {
        console.log(`${indent}   子节点 (${node.children.length}):`);
        printNodeStructure(node.children, level + 2);
      }
      
      if (node.edges && node.edges.length > 0) {
        console.log(`${indent}   内部边 (${node.edges.length}):`);
        node.edges.forEach((edge: any, j: number) => {
          console.log(`${indent}     ${j}: ${edge.sources?.[0]} -> ${edge.targets?.[0]}`);
        });
      }
      console.log();
    });
  }
  
  printNodeStructure(result.children);
  
} else {
  console.log('解析失败:', parseResult.error);
}
