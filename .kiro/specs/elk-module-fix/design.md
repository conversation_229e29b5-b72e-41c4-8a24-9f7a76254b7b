# Design Document

## Overview

The ELK.js module import error is caused by an incorrect import path in the dynamic import statement. Based on the research of the elkjs package structure, the correct approach is to import from the main entry point rather than the specific elk-api.js file. The package.json shows that the main entry is "lib/main" and the elk-api.js file is a browserified bundle that exports ELK as a UMD module.

## Architecture

The solution involves updating the elk-wrapper.ts file to use the correct import path and handle the module structure properly. The architecture will maintain the existing fallback mechanism while fixing the import issue.

### Current Issues
1. Using incorrect import path: `elkjs/lib/elk-api.js`
2. The elk-api.js is a browserified bundle meant for browser environments
3. The main entry point should be used for Node.js/bundler environments

### Proposed Solution
1. Use the main entry point: `elkjs` (which resolves to `lib/main`)
2. Handle both CommonJS and ES module exports properly
3. Maintain backward compatibility with the existing API

## Components and Interfaces

### ELK Wrapper Module (`src/lib/elk-wrapper.ts`)

**Current Implementation Issues:**
- Uses `new Function('return import("elkjs/lib/elk-api.js")')()` which fails
- Assumes elk-api.js exports a constructor directly

**Updated Implementation:**
- Import from `elkjs` main entry point
- Handle the ELK class properly from the main module
- Maintain the same public API for consumers

### Import Strategy

```typescript
// Instead of:
const elkModule = await (new Function('return import("elkjs/lib/elk-api.js")')());

// Use:
const elkModule = await import('elkjs');
```

### Module Structure Handling

The elkjs main module exports an ELK class that needs to be instantiated. The design will:
1. Import the main elkjs module
2. Extract the ELK constructor
3. Create an instance with appropriate options
4. Maintain the existing layout API

## Data Models

No changes to existing data models are required. The ELK wrapper will continue to accept and return the same graph structure:

```typescript
interface GraphData {
  id?: string;
  children?: Node[];
  edges?: Edge[];
  layoutOptions?: LayoutOptions;
}
```

## Error Handling

### Import Error Handling
1. **Primary Strategy**: Try importing from main elkjs module
2. **Fallback Strategy**: If import fails, use the existing mock layouter
3. **Error Logging**: Log specific error messages for debugging

### Runtime Error Handling
1. **Layout Failures**: Catch layout errors and fall back to grid layout
2. **Module Initialization**: Handle cases where ELK constructor is not available
3. **Browser Compatibility**: Ensure the solution works in both Node.js and browser environments

## Testing Strategy

### Unit Tests
1. **Import Success**: Test successful ELK module loading
2. **Import Failure**: Test fallback to mock layouter when import fails
3. **Layout Functionality**: Test that layout operations work correctly
4. **Error Scenarios**: Test various error conditions and fallbacks

### Integration Tests
1. **Graph Rendering**: Test that the fixed ELK wrapper works with the graph visualization
2. **Performance**: Ensure no performance regression with the new import strategy
3. **Browser Compatibility**: Test in different browser environments

### Manual Testing
1. **Development Environment**: Verify the error is resolved in dev mode
2. **Production Build**: Ensure the fix works in production builds
3. **Error Recovery**: Test that fallback mechanisms work properly

## Implementation Notes

### Import Path Resolution
- The elkjs package uses "lib/main" as its main entry point
- This resolves to a CommonJS module that exports the ELK class
- The import should work with both dynamic imports and static imports

### Backward Compatibility
- Maintain the same public API for `performLayout()`, `isElkAvailable()`, and `resetElkInstance()`
- Ensure existing consumers of the elk-wrapper continue to work without changes
- Preserve the fallback behavior when ELK is not available

### Performance Considerations
- Dynamic imports are cached to avoid repeated loading
- The ELK instance is cached after successful creation
- Fallback layouter provides reasonable performance for simple graphs