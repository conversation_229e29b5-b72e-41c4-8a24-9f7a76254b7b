# 需求文档

## 项目介绍

RegexVision 是一个交互式正则表达式可视化解释工具，能够将复杂的正则表达式模式转换为直观的、可交互的可视化图形。该应用提供实时解析、AI 驱动的解释和多向高亮功能，帮助用户从宏观和微观角度理解正则表达式模式。这是一个前端优先的应用，使用 Next.js 构建，具有即时客户端解析和通过 API 路由提供的可选 AI 解释功能。

## 需求

### 需求 1: 交互式正则表达式可视化

**用户故事:** 作为一个学习或调试正则表达式的开发者，我希望能够输入任何正则表达式并看到它被渲染为交互式节点图，这样我就能够直观地理解模式的结构和逻辑流程。

#### 验收标准

1. 当用户输入正则表达式模式时，系统应当使用 regexp-tree 库立即解析它
2. 当解析成功时，系统应当使用 React Flow 将 AST 渲染为交互式节点图
3. 当解析失败时，系统应当显示清晰的错误消息和建议
4. 如果正则表达式包含序列模式，系统应当将它们渲染为顺序节点
5. 如果正则表达式包含选择符 (|)，系统应当将它们渲染为分支节点
6. 如果正则表达式包含量词 (*,+,?,{n,m})，系统应当将它们渲染为修饰符节点
7. 如果正则表达式包含字符类 (\d,\w,[...])，系统应当将它们渲染为字符集节点
8. 如果正则表达式包含分组 (...)，系统应当将它们渲染为容器节点
9. 如果正则表达式包含断言 (^,$,\b)，系统应当将它们渲染为边界节点
10. 当图形被渲染时，节点应当自动布局以获得最佳可读性

### 需求 2: 多向交互高亮

**用户故事:** 作为一个分析正则表达式模式的用户，我希望所有界面元素通过高亮进行同步，这样我就能看到正则表达式的不同部分在可视化图形、源文本、解释和测试结果之间是如何相互关联的。

#### 验收标准

1. 当用户悬停在图形节点上时，系统应当高亮正则表达式输入中的对应文本
2. 当用户悬停在图形节点上时，系统应当高亮对应的解释文本
3. 当用户悬停在图形节点上时，系统应当高亮测试字符串中的匹配部分
4. 当用户悬停在正则表达式源文本上时，系统应当高亮对应的图形节点
5. 当用户悬停在正则表达式源文本上时，系统应当高亮测试字符串中的匹配部分
6. 当用户悬停在解释文本上时，系统应当高亮对应的图形节点和源文本
7. 当高亮激活时，所有相关元素应当使用一致的视觉样式
8. 当用户停止悬停时，所有高亮应当立即移除
9. 当多个元素可能被高亮时，系统应当同时高亮所有相关匹配

### 需求 3: 实时模式测试和匹配

**用户故事:** 作为一个测试正则表达式模式的用户，我希望能够输入测试字符串并立即看到匹配位置的视觉反馈，这样我就能验证我的正则表达式是否按预期工作并快速调试问题。

#### 验收标准

1. 当用户输入测试字符串时，系统应当立即应用正则表达式模式
2. 当找到匹配时，系统应当高亮测试字符串中所有匹配的子字符串
3. 当没有找到匹配时，系统应当清楚地指示没有找到匹配
4. 当用户悬停在特定图形节点上时，系统应当只高亮测试字符串中匹配该特定组件的部分
5. 当存在多个匹配时，系统应当用清晰的视觉区别高亮所有实例
6. 当正则表达式或测试字符串改变时，系统应当通过防抖动 (300ms) 实时更新匹配
7. 当匹配复杂（重叠分组）时，系统应当提供清晰的视觉层次

### 需求 4: AI 驱动的自然语言解释

**用户故事:** 作为一个学习正则表达式的用户，我希望能够获得正则表达式模式每个部分的清晰自然语言解释，这样我就能理解它不仅做什么，还能理解为什么这样工作。

#### 验收标准

1. 当正则表达式成功解析时，系统应当识别可解释的组件
2. 当识别出可解释组件时，系统应当通过 Next.js API 路由请求 AI 解释
3. 当发出 API 请求时，系统应当包含适当的错误处理和重试逻辑
4. 当收到解释时，系统应当缓存它们以避免重复请求
5. 当解释正在加载时，系统应当显示适当的加载状态
6. 当解释可用时，系统应当将它们与对应的图形节点关联显示
7. 当用户选择或悬停在节点上时，系统应当突出显示详细解释
8. 如果 API 请求失败，系统应当优雅降级而不破坏核心功能
9. 当遇到相似的正则表达式模式时，系统应当重用缓存的解释

### 需求 5: 响应式和无障碍界面

**用户故事:** 作为一个在不同设备上使用且有不同无障碍需求的用户，我希望界面能在各种屏幕尺寸上良好工作并且是无障碍的，这样无论我的设备或能力如何，我都能有效地使用这个工具。

#### 验收标准

1. 当在桌面上查看时，系统应当显示多面板布局，所有功能都可见
2. 当在移动设备上查看时，系统应当适应标签页或堆叠布局
3. 当屏幕空间有限时，面板应当可折叠或通过标签页切换
4. 当使用键盘导航时，所有交互元素应当可通过键盘访问
5. 当使用屏幕阅读器时，系统应当提供适当的 ARIA 标签和描述
6. 当文本大小增加时，界面应当保持功能性和可读性
7. 当启用高对比度模式时，系统应当保持视觉清晰度
8. 当禁用动画时，系统应当尊重用户偏好
9. 当界面加载时，核心交互应当在 100ms 内响应

### 需求 6: 性能和可靠性

**用户故事:** 作为一个处理复杂正则表达式模式的用户，我希望工具快速可靠，这样我就能高效工作而不会有延迟或崩溃。

#### 验收标准

1. 当输入正则表达式时，典型模式的解析应当在 50ms 内完成
2. 当渲染图形时，初始显示应当在 200ms 内完成
3. 当发生悬停交互时，高亮应当在 16ms 内响应 (60fps)
4. 当应用加载时，核心功能应当在 1 秒内可用
5. 当内存使用增长时，系统应当实施适当的清理和限制
6. 当输入无效正则表达式时，系统应当优雅地处理错误而不崩溃
7. 当 API 请求超时时，系统应当继续使用本地功能运行
8. 当输入大型正则表达式模式时，系统应当处理它们而不出现性能下降
9. 当发生多个快速输入时，系统应当适当防抖动以保持性能