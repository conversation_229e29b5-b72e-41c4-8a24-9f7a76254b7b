# 设计文档

## 概述

RegexVision 是一个交互式正则表达式可视化解释工具，采用纯前端优先架构。系统将复杂的正则表达式转换为直观的可视化图形，并通过 AI 提供自然语言解释。核心设计理念是实现即时响应的用户体验，所有关键交互都在客户端完成，AI 解释作为增强功能异步加载。

## 架构

### 整体架构

```mermaid
graph TB
    A[用户输入正则表达式] --> B[regexp-tree 解析器]
    B --> C[AST 抽象语法树]
    C --> D[ELK 图形转换器]
    D --> E[React Flow 可视化]
    C --> F[AI 解释服务]
    F --> G[Next.js API Route]
    G --> H[外部 AI API]
    H --> I[缓存解释结果]
    
    J[用户交互] --> K[Zustand 状态管理]
    K --> L[多向高亮系统]
    L --> M[同步更新所有UI组件]
```

### 技术栈架构

- **前端框架**: Next.js 15 (App Router) + TypeScript
- **UI 组件**: Tailwind CSS + shadcn/ui
- **状态管理**: Zustand (轻量级、类型安全)
- **正则解析**: regexp-tree (客户端即时解析)
- **图形可视化**: @xyflow/react (React Flow)
- **自动布局**: elkjs (专业图形布局算法)
- **AI 集成**: Next.js API Routes (代理外部 AI 服务)

### 数据流架构

1. **输入层**: 用户输入 → 防抖处理 (300ms) → 触发解析
2. **解析层**: regexp-tree → AST → ELK 转换 → React Flow 节点
3. **渲染层**: 自动布局计算 → 可视化图形渲染
4. **交互层**: 悬停事件 → 状态更新 → 多向高亮同步
5. **增强层**: AI 解释请求 → 缓存 → 异步显示

## 组件和接口

### 核心组件架构

```
src/
├── app/
│   ├── page.tsx                 # 主页面入口
│   └── api/explain/route.ts     # AI 解释 API 路由
├── components/
│   ├── layout/
│   │   └── MainLayout.tsx       # 主布局组件
│   ├── panels/
│   │   ├── RegexInputPanel.tsx  # 正则输入面板
│   │   ├── GraphPanel.tsx       # 图形可视化面板
│   │   ├── TestPanel.tsx        # 测试面板
│   │   └── ExplanationPanel.tsx # 解释面板
│   └── ui/                      # shadcn/ui 组件
├── lib/
│   ├── parser.ts                # 正则解析器
│   ├── transformer.ts           # AST 到 ELK 转换器
│   ├── layout.ts                # 布局计算引擎
│   └── utils.ts                 # 工具函数
└── store/
    └── regex.store.ts           # Zustand 状态管理
```

### 状态管理接口

```typescript
interface RegexState {
  // 核心数据
  regexString: string;           // 用户输入的正则表达式
  testString: string;            // 测试字符串
  ast: ASTNode | null;           // 解析后的抽象语法树
  
  // 可视化数据
  flowNodes: Node[];             // React Flow 节点数组
  flowEdges: Edge[];             // React Flow 边数组
  
  // AI 解释数据
  aiExplanations: Map<string, string>; // 节点ID -> AI解释映射
  
  // 交互状态
  hoveredElementId: string | null;     // 当前悬停的元素ID
  selectedNodeId: string | null;       // 当前选中的节点ID
  
  // 错误处理
  error: string | null;          // 解析或API错误信息
  isLoading: boolean;            // 加载状态
}

interface RegexActions {
  // 输入处理
  setRegexString: (regex: string) => void;
  setTestString: (text: string) => void;
  
  // 内部处理
  _generateAstAndFlow: () => Promise<void>;
  _fetchExplanationForNode: (nodeId: string, nodeContent: string) => Promise<void>;
  
  // 交互处理
  setHoveredElementId: (id: string | null) => void;
  setSelectedNodeId: (id: string | null) => void;
  
  // 错误处理
  clearError: () => void;
}
```

### 节点类型定义

```typescript
enum NodeType {
  SEQUENCE = 'sequence',         // 序列节点
  ALTERNATION = 'alternation',   // 选择节点 (|)
  QUANTIFIER = 'quantifier',     // 量词节点 (*, +, ?, {n,m})
  CHARACTER_CLASS = 'charClass', // 字符类节点 (\d, \w, [...])
  GROUP = 'group',               // 分组节点 (...)
  ASSERTION = 'assertion',       // 断言节点 (^, $, \b)
  LITERAL = 'literal'            // 字面量节点
}

interface CustomNodeData {
  type: NodeType;
  content: string;               // 节点显示内容
  originalText: string;          // 原始正则文本
  explanation?: string;          // AI 解释
  isLoading?: boolean;           // 解释加载状态
  matches?: string[];            // 在测试字符串中的匹配结果
}
```

## 数据模型

### AST 节点映射

regexp-tree 生成的 AST 节点类型映射到我们的可视化节点：

```typescript
const NODE_TYPE_MAPPING = {
  'Char': NodeType.LITERAL,
  'CharacterClass': NodeType.CHARACTER_CLASS,
  'Quantifier': NodeType.QUANTIFIER,
  'Group': NodeType.GROUP,
  'Alternation': NodeType.ALTERNATION,
  'Assertion': NodeType.ASSERTION,
  'Disjunction': NodeType.ALTERNATION
};
```

### 布局配置

```typescript
interface LayoutConfig {
  algorithm: 'layered' | 'force' | 'stress';
  direction: 'RIGHT' | 'DOWN';
  spacing: {
    nodeNode: number;
    edgeNode: number;
    edgeEdge: number;
  };
  layering: {
    strategy: 'LONGEST_PATH' | 'COFFMAN_GRAHAM';
  };
}
```

### 高亮系统数据模型

```typescript
interface HighlightInfo {
  elementId: string;             // 元素唯一标识
  type: 'node' | 'text' | 'match'; // 高亮类型
  ranges: Array<{               // 高亮范围
    start: number;
    end: number;
    content: string;
  }>;
  relatedElements: string[];     // 关联元素ID列表
}
```

## 错误处理

### 错误类型定义

```typescript
enum ErrorType {
  PARSE_ERROR = 'parse_error',       // 正则解析错误
  LAYOUT_ERROR = 'layout_error',     // 布局计算错误
  API_ERROR = 'api_error',           // AI API 错误
  NETWORK_ERROR = 'network_error'    // 网络错误
}

interface AppError {
  type: ErrorType;
  message: string;
  details?: any;
  timestamp: number;
}
```

### 错误处理策略

1. **解析错误**: 显示友好的错误提示，提供修正建议
2. **布局错误**: 回退到简单布局，保证基本功能
3. **API 错误**: 优雅降级，核心功能继续工作
4. **网络错误**: 显示重试选项，缓存本地状态

### 错误边界组件

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}
```

## 测试策略

### 单元测试

- **解析器测试**: 测试各种正则表达式的解析正确性
- **转换器测试**: 测试 AST 到图形节点的转换逻辑
- **状态管理测试**: 测试 Zustand store 的状态变更
- **工具函数测试**: 测试高亮、匹配等工具函数

### 集成测试

- **端到端流程**: 从输入到可视化的完整流程测试
- **交互测试**: 悬停、点击等用户交互测试
- **API 集成测试**: AI 解释服务的集成测试

### 性能测试

- **解析性能**: 复杂正则表达式的解析时间测试
- **渲染性能**: 大型图形的渲染性能测试
- **内存使用**: 长时间使用的内存泄漏测试

### 测试工具

- **单元测试**: Jest + React Testing Library
- **E2E 测试**: Playwright
- **性能测试**: Chrome DevTools + Lighthouse
- **类型检查**: TypeScript 严格模式

### 测试覆盖率目标

- 核心逻辑函数: 90%+
- 组件渲染: 80%+
- 错误处理: 95%+
- 整体覆盖率: 85%+