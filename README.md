# RegexVision - 交互式正则可视化解释器

一款能将任何正则表达式瞬间转化为**可交互的、可视化的逻辑图**和清晰解释的工具。

## 🚀 快速开始

### 1. 环境配置

首先，复制环境变量示例文件并配置 API Key：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，添加你的 OpenRouter API Key：

```env
# 从 https://openrouter.ai/keys 获取你的 API Key
OPENROUTER_API_KEY=your_openrouter_api_key_here

# 可选：网站 URL（用于 OpenRouter 排名）
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 2. 安装依赖

```bash
npm install
```

### 3. 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 🧪 API 测试

### 测试 OpenRouter 直接集成

```bash
# 设置环境变量并测试 OpenRouter API
OPENROUTER_API_KEY=your_key_here node test-openrouter-api.js
```

### 测试 Next.js API 路由

```bash
# 1. 启动开发服务器
npm run dev

# 2. 在另一个终端测试 API 路由
node test-api-route.js
```

## 🏗️ 技术栈

- **框架**: Next.js (App Router)
- **语言**: TypeScript
- **UI**: Tailwind CSS + shadcn/ui
- **状态管理**: Zustand
- **正则解析**: regexp-tree
- **图形可视化**: @xyflow/react (React Flow)
- **AI 服务**: OpenRouter API (Claude 3.5 Sonnet)

## 📖 API 文档

### POST /api/explain

解释正则表达式片段的 API 端点。

**请求体:**
```json
{
  "snippet": "\\d+"
}
```

**成功响应 (200):**
```json
{
  "explanation": "匹配一个或多个数字字符"
}
```

**错误响应:**
```json
{
  "error": "错误描述",
  "code": "ERROR_CODE"
}
```

**错误代码:**
- `INVALID_JSON`: 请求体不是有效的 JSON
- `INVALID_SNIPPET`: 缺少或无效的 snippet 字段
- `SERVICE_NOT_CONFIGURED`: API Key 未配置
- `UPSTREAM_ERROR`: OpenRouter API 错误
- `TIMEOUT`: 请求超时
- `NETWORK_ERROR`: 网络连接错误

## 🧪 运行测试

```bash
# 运行所有测试
npm test

# 运行测试并显示 UI
npm run test:ui

# 运行测试一次（CI 模式）
npm run test:run
```
