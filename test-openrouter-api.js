/**
 * 测试 OpenRouter API 集成
 * 运行方式: node test-openrouter-api.js
 */

// 使用 Node.js 18+ 内置的 fetch
// 如果是较老版本的 Node.js，请安装 node-fetch: npm install node-fetch

async function testOpenRouterAPI() {
  console.log('🧪 测试 OpenRouter API 集成...\n');

  // 检查环境变量
  const apiKey = process.env.OPENROUTER_API_KEY;
  if (!apiKey) {
    console.error('❌ 错误: 请设置 OPENROUTER_API_KEY 环境变量');
    console.log('💡 提示: 从 https://openrouter.ai/keys 获取 API Key');
    process.exit(1);
  }

  console.log('✅ API Key 已配置');

  // 测试数据
  const testSnippet = '\\d+';
  
  const requestBody = {
    model: "anthropic/claude-3.5-sonnet",
    messages: [
      {
        role: "system",
        content: `你是一位精通所有正则表达式方言的专家。你的任务是为给定的正则表达式片段，提供一个简洁、清晰、且对初学者友好的解释。

这个片段是：\`${testSnippet}\`

请直接返回解释文本，不要添加任何"好的"、"当然"等多余的开场白或结束语。`
      },
      {
        role: "user",
        content: testSnippet
      }
    ]
  };

  try {
    console.log('🚀 发送请求到 OpenRouter...');
    console.log(`📝 测试片段: ${testSnippet}`);

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'RegexVision - Interactive Regex Visualizer'
      },
      body: JSON.stringify(requestBody)
    });

    console.log(`📊 响应状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API 请求失败:');
      console.error(errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ API 请求成功!');
    
    const explanation = data?.choices?.[0]?.message?.content;
    if (explanation) {
      console.log('\n📖 AI 解释:');
      console.log('─'.repeat(50));
      console.log(explanation);
      console.log('─'.repeat(50));
    } else {
      console.error('❌ 响应格式错误: 未找到解释内容');
      console.log('🔍 完整响应:', JSON.stringify(data, null, 2));
    }

    // 显示使用统计
    if (data.usage) {
      console.log('\n📈 使用统计:');
      console.log(`   提示词 tokens: ${data.usage.prompt_tokens}`);
      console.log(`   完成 tokens: ${data.usage.completion_tokens}`);
      console.log(`   总计 tokens: ${data.usage.total_tokens}`);
      if (data.usage.cost) {
        console.log(`   费用: ${data.usage.cost} credits`);
      }
    }

  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }
}

// 运行测试
testOpenRouterAPI();
