# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Next.js 15.4.1 project with App Router, TypeScript, Tailwind CSS v4, and shadcn/ui configured.

## Key Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Architecture

### Structure
- `src/app/` - App Router (pages, layouts, loading states)
- `src/lib/utils.ts` - Utility functions (cn helper for conditional classes)
- `public/` - Static assets
- `components.json` - shadcn/ui configuration

### Technology Stack
- **Next.js 15.4.1** with App Router and React Server Components
- **React 19.1.0**
- **TypeScript** with strict mode
- **Tailwind CSS v4** (CSS-first approach)
- **shadcn/ui** with "new-york" style
- **lucide-react** for icons

### Key Patterns
- Path aliases: `@/` maps to `src/`
- CSS variables for theming (light/dark mode support)
- Utility-first styling via Tailwind
- Component-based architecture with shadcn/ui

### Adding Components
Use shadcn/ui to add components:
```bash
npx shadcn@latest add [component-name]
```

### Configuration Files
- `next.config.ts` - Next.js configuration
- `tailwind.config.ts` - Not needed (v4 uses CSS-first)
- `components.json` - shadcn/ui configuration
- `tsconfig.json` - TypeScript with path mapping