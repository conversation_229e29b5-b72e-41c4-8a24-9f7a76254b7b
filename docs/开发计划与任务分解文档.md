# 开发计划与任务分解文档 (RegexVision V0.2.0)



- **文档目的**: 本文档作为项目的“施工图”，定义了从 0 到 1 实现 MVP 的所有技术任务、顺序和关键实现细节。它是我（项目负责人）与 AI 编程助手协作的唯一指令来源。
- **关联文档**: MVP 需求文档：交互式正则可视化解释器 (V0.2.0)

------



## 1. 总体开发策略 (Overall Development Strategy)

### 1.1. 技术选型 (Final Tech Stack)

- **框架**: Next.js (App Router)
- **语言**: TypeScript
- **UI**: Tailwind CSS + shadcn/ui
- **状态管理**: Zustand
- **正则解析**: `regexp-tree`
- **图形可视化**: `@xyflow/react` (React Flow)

### 1.2. 核心架构 (Core Architecture)

本应用为纯前端优先架构。

1. **结构解析 (Structure)**: 客户端使用 `regexp-tree` 库，实现**即时**的正则表达式 -> AST (抽象语法树) 的转换。这是所有可视化和交互的基础。
2. **语义解释 (Semantics)**: 客户端将正则片段通过 Next.js API Route 发送到后端，后端调用大模型 API 获取自然语言解释。**后端仅作 API 代理，不处理业务逻辑**。
3. **状态驱动**: 应用的 UI 完全由中心化的 Zustand Store 驱动。用户操作 -> 修改 Store -> 触发 UI 重新渲染。



### 1.3. 开发流程 (Development Flow)



我们将严格按照以下分层顺序进行开发，确保每一步都有坚实的基础： `项目初始化` -> `状态管理` -> `静态 UI` -> `核心引擎` -> `AI 集成` -> `交互逻辑`

------



## 2. 任务分解 (Task Breakdown)

### ✅ **任务 1: 项目初始化 (Project Setup)**



- [x] 使用 `create-next-app` 创建项目。
- [x] 初始化 `tailwind.config.ts` 和 `globals.css`。
- [x] 使用 `npx shadcn-ui@latest init` 初始化 `shadcn/ui`。
- [x] **执行:** `npm install zustand @xyflow/react regexp-tree`。



### **任务 2: 状态管理层 (State Management - Zustand)**



- [ ] 创建 `store/regex.store.ts` 文件。

- [ ] 在该文件中，定义并创建一个 Zustand Store，其 State 和 Actions 必须符合以下 TypeScript 接口：

  TypeScript

  ```
  // store/regex.store.ts
  
  import { Node, Edge } from '@xyflow/react';
  
  // 定义 AST 节点的类型（可以先用 any，后续细化）
  type ASTNode = any; 
  
  interface RegexState {
    regexString: string;
    testString: string;
    ast: ASTNode | null;
    flowNodes: Node[];
    flowEdges: Edge[];
    aiExplanations: Map<string, string>; // key: AST 节点的唯一标识, value: AI 解释
    hoveredElementId: string | null;
    error: string | null; // 用于存放解析或 API 错误
  }
  
  interface RegexActions {
    setRegexString: (regex: string) => void;
    setTestString: (text: string) => void;
    // 内部调用，由 setRegexString 触发
    _generateAstAndFlow: () => void; 
    _fetchExplanationForNode: (nodeId: string, nodeContent: string) => Promise<void>;
    setHoveredElementId: (id: string | null) => void;
  }
  ```



### **任务 3: UI 静态布局 (Static UI Layout)**



- [ ] **任务 3.1**: 创建 `components/layout/MainLayout.tsx`。使用 `shadcn/ui` 的 `<Resizable>` 组件搭建左右分栏的主体布局。此时不包含任何逻辑。
- [ ] **任务 3.2**: 创建 `components/panels/RegexInputPanel.tsx`。包含一个 `<Textarea>`，接收 `value` 和 `onChange` props。
- [ ] **任务 3.3**: 创建 `components/panels/GraphPanel.tsx`。内部使用 `<ReactFlow />` 组件，接收 `nodes` 和 `edges` props，并渲染出**固定的、用于测试的**节点和边。
- [ ] **任务 3.4**: 将以上所有静态组件组合在 `app/page.tsx` 中，确保布局和样式正确无误。



### **任务 4: 核心引擎实现 (Core Engine) - (升级版：集成自动布局)**

**说明**: 此任务已更新，集成了 `elkjs` 库来实现专业的自动布局，灵感来源于 `jsoncrack.com`。请在开始前执行 `npm install elkjs`。

- [ ] **任务 4.1: 解析器**
  - **文件**: `lib/parser.ts`
  - **目标**: 创建函数 `parseRegex(regexString: string)`。
  - **实现**: 使用 `regexp-tree` 解析正则表达式字符串为 AST。包含 `try-catch` 块处理错误。
  - *(此任务无变化)*
- [ ] **任务 4.2: AST 到 ELK 图转换器**
  - **文件**: `lib/transformer.ts`
  - **目标**: 创建函数 `astToElkGraph(ast: ASTNode)`。
  - **实现**: 此函数的核心任务是将 `regexp-tree` 生成的 AST，递归转换为 `elkjs` 库能理解的层级图结构。输出的图对象应包含 `id`, `children` 数组, 和 `edges` 数组等属性。
- [ ] **任务 4.3: ELK 自动布局计算与格式转换**
  - **文件**: `lib/layout.ts` (建议新建)
  - **目标**: 创建一个**异步**函数 `getLayoutedElements(ast: ASTNode)`。
  - **实现**: 这个函数将成为我们新的核心“翻译官”，它会串联整个流程：
    1. **实例化 ELK**: `const elk = new ELK()`。
    2. **调用转换器**: 调用上一步的 `astToElkGraph(ast)` 得到 ELK 能理解的图结构。
    3. **计算布局**: 调用 `elk.layout(graph)` 来异步计算所有节点和边的精确位置 (`x`, `y`) 和尺寸。
    4. **转换为 Flow 格式**: 遍历经过布局计算后的图，将其中的节点和边最终转换为 React Flow 能直接渲染的 `{ nodes: Node[], edges: Edge[] }` 格式。
    5. 返回包含 `nodes` 和 `edges` 的对象。
- [ ] **任务 4.4: 连接逻辑**
  - **文件**: `store/regex.store.ts`
  - **目标**: 修改 `_generateAstAndFlow` action。
  - **实现**:
    1. 将 `_generateAstAndFlow` action 修改为**异步函数** (`async`)。
    2. 在 action 内部，首先调用 `parseRegex` 生成 AST。
    3. 如果 AST 有效，则 `await` 调用上一步的 `getLayoutedElements(ast)` 函数，一次性获取到经过完整布局计算的 `nodes` 和 `edges`。
    4. 用获取到的结果更新 `flowNodes` 和 `flowEdges` 状态。
    5. 在整个过程中添加 `try-catch` 块，并在 `catch` 中更新 `error` 状态，同时清空 `flowNodes` 和 `flowEdges`。



### **任务 5: AI 服务集成 (AI Service Integration)**



- [ ] **任务 5.1: API 路由**: 创建 `app/api/explain/route.ts`。这是一个 `POST` 路由，接收 `{"snippet": "..."}` 格式的 body。

- [ ] **任务 5.2: Prompt 设计**: 在 API 路由内部，使用以下 Prompt 模板调用大模型：

  ```
  你是一位精通所有正则表达式方言的专家。你的任务是为给定的正则表达式片段，提供一个简洁、清晰、且对初学者友好的解释。
  
  这个片段是：`${snippet}`
  
  请直接返回解释文本，不要添加任何“好的”、“当然”等多余的开场白或结束语。
  ```

- [ ] **任务 5.3: 前端调用**: 在 Zustand Store 中实现 `_fetchExplanationForNode` action。它将异步调用 `/api/explain`，并将返回的解释存入 `aiExplanations` Map 中。



### **任务 6: 交互逻辑实现 (Interaction Logic)**



- [ ] **任务 6.1: 节点悬停**: 在 `GraphPanel.tsx` 中，为 `<ReactFlow />` 组件添加 `onNodeMouseEnter` 和 `onNodeMouseLeave` 事件处理器。这些处理器分别调用 Store 中的 `setHoveredElementId(node.id)` 和 `setHoveredElementId(null)`。
- [ ] **任务 6.2: 全局高亮**: 修改所有需要高亮的组件（如 `GraphPanel` 中的节点、`RegexInputPanel` 中被拆分的 `<span>` 等），使其样式根据 Store 中的 `hoveredElementId` 动态变化。