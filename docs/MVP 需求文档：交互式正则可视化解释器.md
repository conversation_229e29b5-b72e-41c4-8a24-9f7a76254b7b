# MVP 需求文档：交互式正则可视化解释器 (V0.2.0)



- **产品名称**: RegexVision (暂定)
- **版本**: 0.2.0 (MVP)
- **日期**: 2025-07-12
- **目标**: 打造一款能将任何正则表达式瞬间转化为**可交互的、可视化的逻辑图**和清晰解释的纯前端优先工具。
- **技术栈**: `Next.js`, `TailwindCSS`, `TypeScript`, `shadcn/ui`

------

## 1. 核心功能 (Core Features)

### US-1: 主功能 - 正则表达式的可视化解构与解释



**用户故事**: 作为一个用户，我希望能粘贴任意一段正则表达式，系统能立即将其**渲染成一个类似流程图的、可交互的节点图**，并配以清晰的文字说明，让我能从宏观到微观彻底理解它的工作原理。

**功能需求 (Functional Requirements):**

- **FR-1.1**: **正则输入区**: 使用 `shadcn/ui` 的 `<Input />` 或 `<Textarea />` 组件。输入内容变化时，实时触发解析流程（debounce 300ms）。
- **FR-1.2**: **可视化图形渲染区 (Visual Graph Area)**
  - 必须有一个专门的区域，用于将解析后的正则表达式渲染成一个节点图。
  - **节点类型应包括**:
    - `Sequence`: 代表串行匹配的节点。
    - `Alternation` (或 `|`): 代表“或”逻辑的分支节点。
    - `Quantifier` (例如 `*`, `+`, `?`, `{n,m}`): 跟随在其他节点后，表示重复次数的节点。
    - `Character Class` (例如 `\d`, `\w`, `[...]`): 代表匹配特定字符集的节点。
    - `Group` (例如 `(...)`): 代表捕获或非捕获分组的容器节点。
    - `Assertion` (例如 `^`, `$`, `\b`): 代表边界断言的节点。
  - **强烈建议使用 `React Flow` (`@xyflow/react`) 库来实现**，它非常适合构建此类节点图应用。
- **FR-1.3**: **混合解析架构 (Hybrid Parsing Architecture)**
  - **前端解析**: 必须使用 `regexp-tree` 库在客户端进行即时解析，生成 AST。**此 AST 是驱动 FR-1.2 (可视化图形) 和 US-2 (交互高亮) 的核心数据源**。
  - **AI 解释**:
    - 为 AST 中的每个节点/片段，通过 **Next.js API Route** 向后端发送请求。
    - 后端 API Route 负责调用外部大模型 API (如 Claude/GPT) 获取该片段的自然语言解释。
    - API Route 起到**隐藏和管理 API Key** 的作用。
    - 获取到解释后，在前端的对应节点上展示出来。**需要实现缓存机制**，避免对相同正则片段的重复请求。
- **FR-1.4**: **文本解释区 (Text Explanation Area)**
  - 作为图形的补充，或在节点被选中时，在侧边栏显示由 AI 生成的、更详细的分步文本解释。使用 `shadcn/ui` 的 `<Card />` 组件进行美观的展示。



### US-2: 核心爽点 - 全方位交互高亮与调试



**用户故事**: 我希望整个界面是联动的，当我与**可视化图、正则原文、解释文本、测试结果**中的任何一部分交互时，其他所有相关部分都能立即高亮响应，给我提供一个完全沉浸的调试体验。

**功能需求 (Functional Requirements):**

- **FR-2.1**: **多向交互高亮 (Multi-directional Highlighting)**
  - **图形 -> 其他**: 鼠标悬停在**可视化图的某个节点**上时，**正则原文**、**解释文本**以及**测试字符串匹配部分 (FR-3.3)** 中对应的部分都应同步高亮。
  - **原文 -> 其他**: 悬停在正则原文的某个片段上时，图形中的对应节点、解释文本、测试匹配部分也应同步高亮。
  - **解释 -> 其他**: 悬停在某条解释文本上时，其他部分也应同步高亮。
- **FR-2.2**: **实时匹配与调试**
  - 提供一个测试字符串输入区 (使用 `<Textarea />`)。
  - **全局匹配高亮**: 在测试字符串中高亮所有匹配整个正则的子串。
  - **组件级匹配高亮 (关键)**: 实现 FR-2.1 中描述的联动，当悬停在任何一个逻辑组件上时，能精准高亮它在测试字符串中匹配到的**所有**子片段。

------

## 2. 非功能性需求 (Non-Functional Requirements)

- **性能**: 客户端图形渲染和交互必须流畅。AST 解析和基础绘图必须是即时的。AI 解释的延迟是可接受的，但需要有加载状态（Loading skeletons，可使用 `shadcn/ui` 的 `<Skeleton />`）。
- **响应式设计 (Responsive Design)**:
  - 在桌面端，显示多面板布局。
  - 在移动端（小屏幕），面板应自动折叠为可切换的标签页 (Tabs, 使用 `shadcn/ui` 的 `<Tabs />`) 或垂直堆叠的卡片，确保核心功能可用。
- **无后端依赖 (MVP)**: 核心交互功能纯前端，仅通过 API Route 调用外部 AI 服务。无数据库，无用户状态存储。