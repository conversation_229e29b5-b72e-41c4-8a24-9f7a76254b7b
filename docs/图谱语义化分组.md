### 任务：重构核心转换逻辑，实现语义化分组（第一阶段）

**目标**: 我们需要彻底重构 `lib/transformer.ts` 中的 `astToElkGraph` 函数。当前它生成了过多底层细节，导致可视化结果混乱。我们的新目标是引入“宏观节点” (Macro Nodes) 的概念，对 AST 进行语义化分组，只展示对用户有价值的结构。

**当前阶段范围**: 我们只重构数据转换层，暂时不创建任何新的 React UI 组件。首要目标是实现对**连续普通字符**的合并。

### **给 AI 的详细指令**

你好，请根据以下详细规格，帮我重写 `lib/transformer.ts` 中的 `astToElkGraph` 函数。

#### 1. 函数签名与基本结构

- 函数签名保持不变：`export function astToElkGraph(ast: ASTNode): ElkNode`。
- 函数内部需要一个递归的辅助函数，例如 `traverse(subAst: ASTNode, parentId: string)`，它负责处理 AST 的每个子节点，并返回一个包含节点和边的对象 `{ nodes: ElkNode[], edges: ElkEdge[] }`。

#### 2. 核心逻辑：处理 `Sequence` 类型的节点

这是本次重构的核心。当 `traverse` 函数遇到一个类型为 `Sequence` 的 AST 节点时，你必须执行以下“合并”逻辑，而不是简单地为它的每一个子节点创建节点：

**伪代码/逻辑步骤:**

```typescript
function handleSequenceNode(sequenceAstNode, parentId):
  let nodes = []
  let edges = []
  let charBuffer = [] // 用于存储连续的 Char 节点
  let lastNodeId = parentId

  // 遍历 Sequence 的所有子表达式
  for expression in sequenceAstNode.expressions:
    if expression.type === 'Char':
      // 如果是普通字符，先存入缓冲区
      charBuffer.push(expression)
    else:
      // 遇到非字符节点，先处理缓冲区
      if charBuffer.length > 0:
        // 调用一个新函数来处理缓冲区中的字符
        let { newNode, newEdge } = processCharBuffer(charBuffer, lastNodeId)
        nodes.push(newNode)
        edges.push(newEdge)
        lastNodeId = newNode.id
        charBuffer = [] // 清空缓冲区

      // 递归处理这个非字符节点
      let { childNodes, childEdges } = traverse(expression, lastNodeId)
      nodes.push(...childNodes)
      edges.push(...childEdges)
      // 更新最后一个节点的 ID，以便连接下一个节点
      if (childNodes.length > 0) {
        lastNodeId = findLastNodeId(childNodes) // 你需要实现一个找到图中最后一个节点的逻辑
      }
  
  // 循环结束后，别忘了处理可能剩下的最后一个缓冲区
  if charBuffer.length > 0:
    let { newNode, newEdge } = processCharBuffer(charBuffer, lastNodeId)
    nodes.push(newNode)
    edges.push(newEdge)

  return { nodes, edges }
```

#### 3. 新的辅助函数：`processCharBuffer`

你需要创建一个新的辅助函数来处理字符缓冲区。

**伪代码/逻辑步骤:**

```typescript
function processCharBuffer(buffer, previousNodeId):
  // 1. 合并文本
  let combinedText = buffer.map(char => char.value).join('')

  // 2. 创建新的宏观节点
  let newNode = {
    id: 'macro_sequence_' + generateUniqueId(),
    type: 'macro_sequence', // 新的自定义节点类型
    width: calculateNodeWidth(combinedText), // 根据文本长度计算一个合适的宽度
    height: 40,
    data: {
      label: combinedText,
      raw: combinedText, // 保存原始文本
      type: 'Sequence'
    }
  }

  // 3. 创建连接到上一个节点的边
  let newEdge = {
    id: 'edge_' + generateUniqueId(),
    sources: [previousNodeId],
    targets: [newNode.id]
  }

  return { newNode, newEdge }
```

#### 4. 对其他节点类型的初步处理

在 `traverse` 函数的 `switch` 语句中，对于我们暂时不处理的其他节点类型（如 `Quantifier`, `Alternation` 等），请先进行简单的递归调用，或者创建一个带有原始类型标签的“占位节点”，以便我们后续迭代。

请根据以上详细的、包含伪代码的规格，开始重构 `astToElkGraph` 函数。