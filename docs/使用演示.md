# 交互式正则表达式可视化工具 - 使用演示

## 🚀 快速开始

### 1. 启动应用
```bash
npm run dev
```
访问：http://localhost:3003

### 2. 体验交互功能

#### 示例 1：简单字符匹配
1. 在正则表达式输入框中输入：`hello`
2. 将鼠标悬停在 `hello` 上
3. 观察右侧图表中对应节点的蓝色高亮效果

#### 示例 2：数字匹配
1. 输入：`\d+`
2. 悬停在 `\d+` 上
3. 查看右侧图表节点高亮

#### 示例 3：复合表达式
1. 输入：`abc\d+xyz`
2. 分别悬停在：
   - `abc` - 查看字符序列节点高亮
   - `\d+` - 查看数字匹配节点高亮  
   - `xyz` - 查看另一个字符序列节点高亮

#### 示例 4：字符类
1. 输入：`[a-z]+`
2. 悬停在 `[a-z]+` 上
3. 观察字符类节点的高亮效果

### 3. 编辑功能演示

1. **进入编辑模式**
   - 点击正则表达式输入框右上角的"编辑"按钮
   - 输入框变为可编辑状态

2. **修改正则表达式**
   - 尝试修改为：`\w+@\w+\.\w+`（邮箱匹配）
   - 点击"保存"按钮确认修改

3. **取消编辑**
   - 在编辑模式下点击"取消"按钮
   - 恢复到之前的状态

## 🎯 功能特点

### 双向交互高亮
- **从输入框到图表**：悬停正则表达式片段 → 图表节点高亮
- **从图表到输入框**：悬停图表节点 → 正则表达式片段高亮

### 智能片段识别
- 自动识别正则表达式中的不同组件
- 将复杂表达式分解为可理解的片段
- 每个片段对应图表中的特定节点

### 视觉反馈
- **蓝色高亮**：表示可交互区域
- **脉冲指示器**：提示交互功能可用
- **平滑过渡**：优雅的悬停动画效果

## 📝 推荐测试用例

### 基础测试
```regex
abc          # 简单字符串
\d           # 单个数字
\w+          # 一个或多个单词字符
[a-z]        # 字符类
```

### 中级测试
```regex
\d{3}-\d{4}     # 电话号码格式
[A-Z][a-z]+     # 首字母大写的单词
\w+@\w+\.\w+    # 简单邮箱格式
```

### 高级测试
```regex
^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$  # 完整邮箱验证
(\d{4})-(\d{2})-(\d{2})                           # 日期格式（带分组）
```

## 🔧 故障排除

### 如果交互不工作
1. 确保正则表达式有效
2. 检查是否有对应的图表节点生成
3. 尝试刷新页面

### 如果高亮效果不明显
1. 确保鼠标完全悬停在文字上
2. 检查浏览器是否支持 CSS 过渡效果
3. 尝试不同的正则表达式

### 编辑功能问题
1. 确保点击了"编辑"按钮
2. 修改后记得点击"保存"
3. 无效的正则表达式可能不会生成图表

## 💡 使用技巧

1. **从简单开始**：先用简单的正则表达式熟悉交互
2. **观察模式**：注意不同类型的正则元素如何在图表中表示
3. **实验学习**：尝试修改正则表达式，观察图表变化
4. **组合使用**：结合测试字符串功能，全面理解正则表达式行为

## 🎉 享受学习过程

这个交互式工具让正则表达式学习变得直观有趣。通过可视化的方式，你可以：
- 理解正则表达式的结构
- 学习不同元素的作用
- 调试复杂的正则表达式
- 提高正则表达式编写技能

开始探索吧！🚀
