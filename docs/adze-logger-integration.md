# Adze Logger 集成指南

## 概述

本文档介绍如何在 RegexAI 项目中集成和使用 Adze 日志库。Adze 是一个现代化的、功能丰富的 JavaScript/TypeScript 日志库，提供了更好的日志格式化、过滤和中间件支持。

## 安装

Adze 已经通过 npm 安装到项目中：

```bash
npm install adze
```

## 配置文件

### 主要配置文件：`src/lib/adze-logger.ts`

这个文件包含了 Adze 的全局配置和便捷的日志方法：

- **环境适配**：开发环境使用 `pretty` 格式，生产环境使用 `json` 格式
- **日志级别**：通过 `LOG_LEVEL` 环境变量控制
- **缓存**：启用日志缓存，缓存大小为 500
- **命名空间**：为不同功能模块提供专门的日志实例
- **监听器**：为错误和警报日志添加特殊处理

## 使用方法

### 1. 基础日志

```typescript
import { adzeLog } from '../lib/adze-logger';

// 基础日志方法
adzeLog.debug('调试信息', { userId: 123 });
adzeLog.info('信息日志', { component: 'RegexParser' });
adzeLog.warn('警告信息', { issue: 'performance' });
adzeLog.error('错误信息', new Error('示例错误'));
adzeLog.success('成功信息');
adzeLog.fail('失败信息');
```

### 2. API 日志

```typescript
// API 请求日志
adzeLog.api.request('POST', '/api/regex/parse', { pattern: '/test/g' });

// API 响应日志
adzeLog.api.response('POST', '/api/regex/parse', 200, 45, { result: 'success' });

// API 错误日志
adzeLog.api.error('GET', '/api/regex/history', new Error('Database error'));
```

### 3. 性能日志

```typescript
// 方式1：计时器模式
const timer = adzeLog.performance.start('正则表达式解析');
// ... 执行操作
const duration = timer.end({ pattern: '/test/g', nodes: 5 });

// 方式2：直接记录
adzeLog.performance.measure('图谱渲染', 234.56, { nodeCount: 20 });
```

### 4. UI 交互日志

```typescript
// UI 交互日志
adzeLog.ui.click('regex-input-button', { position: { x: 100, y: 200 } });
adzeLog.ui.input('regex-pattern', '/test/g');
adzeLog.ui.navigation('home', 'regex-editor');
adzeLog.ui.error('RegexVisualization', new Error('Canvas error'));
```

### 5. 正则表达式专用日志

```typescript
// 正则表达式处理日志
adzeLog.regex.parse('/hello.*world/gi', true, { flags: ['g', 'i'] });
adzeLog.regex.match('/test/g', 'test string', 2, { matches: ['test', 'test'] });
adzeLog.regex.error('/(?<invalid/g', new Error('Invalid group'));
```

## 从现有 Logger 迁移

### 迁移对照表

| 现有 Logger | Adze Logger | 说明 |
|------------|-------------|------|
| `logger.debug(msg, ctx)` | `adzeLog.debug(msg, ctx)` | 直接替换 |
| `logger.info(msg, ctx)` | `adzeLog.info(msg, ctx)` | 直接替换 |
| `logger.warn(msg, ctx)` | `adzeLog.warn(msg, ctx)` | 直接替换 |
| `logger.error(msg, err, ctx)` | `adzeLog.error(msg, err, ctx)` | 直接替换 |
| `logger.apiRequest(method, path, ctx)` | `adzeLog.api.request(method, path, ctx)` | 使用专用 API 方法 |
| `logger.apiResponse(method, path, status, duration, ctx)` | `adzeLog.api.response(method, path, status, duration, ctx)` | 使用专用 API 方法 |
| `logger.performance(op, duration, ctx)` | `adzeLog.performance.measure(op, duration, ctx)` | 使用专用性能方法 |

### 渐进式迁移策略

1. **保持现有 Logger**：不要立即删除现有的 logger，保持向后兼容
2. **新功能使用 Adze**：所有新开发的功能使用 Adze Logger
3. **逐步替换**：在维护现有代码时，逐步将日志调用替换为 Adze
4. **测试验证**：确保日志输出符合预期后再完全切换

## 环境变量配置

### 开发环境 (.env.local)

```bash
# 日志级别：debug, info, warn, error
LOG_LEVEL=debug
NODE_ENV=development
```

### 生产环境

```bash
# 生产环境建议使用 info 或 warn 级别
LOG_LEVEL=info
NODE_ENV=production
```

### 测试环境

```bash
# 测试环境默认静默，除非明确设置
LOG_LEVEL=error
NODE_ENV=test
```

## 高级功能

### 1. 自定义命名空间

```typescript
import { adzeLogger } from '../lib/adze-logger';

// 创建自定义命名空间的日志实例
const customLogger = adzeLogger.ns('CustomModule').seal();
customLogger.info('来自自定义模块的日志');
```

### 2. 日志过滤

可以通过环境变量或配置来过滤特定命名空间的日志：

```typescript
import { adzeStore } from '../lib/adze-logger';

// 添加自定义过滤器
adzeStore.addListener('error', (log) => {
  // 只处理来自特定命名空间的错误
  if (log.data.namespace?.includes('Critical')) {
    // 发送到监控系统
    sendToMonitoring(log);
  }
});
```

### 3. 中间件扩展

Adze 支持中间件来扩展功能，比如将日志发送到外部服务：

```typescript
// 可以添加文件传输中间件（需要额外安装）
// import AdzeFileTransport from '@adze/transport-file';
```

## 最佳实践

1. **使用合适的日志级别**：
   - `debug`：详细的调试信息
   - `info`：一般信息
   - `warn`：警告信息
   - `error`：错误信息
   - `success`/`fail`：操作结果

2. **提供上下文信息**：
   ```typescript
   adzeLog.error('正则表达式解析失败', error, {
     pattern: userInput,
     userId: currentUser.id,
     timestamp: Date.now()
   });
   ```

3. **使用专用的日志方法**：
   - API 相关使用 `adzeLog.api.*`
   - 性能相关使用 `adzeLog.performance.*`
   - UI 相关使用 `adzeLog.ui.*`
   - 正则相关使用 `adzeLog.regex.*`

4. **避免敏感信息**：
   ```typescript
   // ❌ 不要记录敏感信息
   adzeLog.info('用户登录', { password: userPassword });
   
   // ✅ 只记录必要信息
   adzeLog.info('用户登录', { userId: user.id, timestamp: Date.now() });
   ```

## 示例代码

查看 `src/examples/adze-logger-example.ts` 文件获取完整的使用示例。

## 故障排除

### 1. 日志不显示

检查环境变量 `LOG_LEVEL` 设置是否正确，确保日志级别足够低。

### 2. 测试环境日志过多

测试环境默认静默日志，如需在测试中查看日志，请设置 `LOG_LEVEL` 环境变量。

### 3. 性能问题

如果日志过多影响性能，可以：
- 提高日志级别（如从 `debug` 改为 `info`）
- 减少缓存大小
- 在生产环境禁用某些详细日志

## 总结

Adze Logger 为 RegexAI 项目提供了更强大和灵活的日志功能。通过合理使用不同的日志方法和命名空间，可以更好地监控和调试应用程序。建议采用渐进式迁移策略，逐步从现有的 Logger 切换到 Adze Logger。
