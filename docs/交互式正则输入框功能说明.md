# 交互式正则表达式输入框功能说明

## 功能概述

我们成功实现了交互式正则表达式输入框组件 (`InteractiveRegexInput`)，该组件取代了原来的简单 `<Textarea>` 输入框，提供了以下核心功能：

### 🎯 核心功能

1. **智能字符串分割**
   - 将正则表达式字符串动态分割成对应图表节点的片段
   - 每个片段都与右侧图表中的特定节点关联

2. **双向交互高亮**
   - 鼠标悬停在正则表达式片段上时，右侧对应的图表节点会高亮
   - 鼠标悬停在图表节点上时，左侧对应的正则表达式片段会高亮

3. **编辑功能**
   - 支持只读模式和编辑模式切换
   - 编辑模式下可以修改正则表达式
   - 保存/取消编辑功能

4. **视觉反馈**
   - 交互片段有明显的悬停效果
   - 高亮状态有清晰的视觉区分
   - 提供用户操作提示

## 技术实现

### 组件结构

```
components/panels/InteractiveRegexInput.tsx
├── 智能字符串分割算法
├── 交互事件处理
├── 编辑模式切换
└── 视觉样式优化
```

### 核心算法

**字符串分割算法**：
- 基于节点内容长度排序（长的优先匹配）
- 避免重叠匹配的范围检测
- 字符映射数组生成连续片段

**交互逻辑**：
- `onMouseEnter`: 调用 `setHoveredElementId(nodeId)`
- `onMouseLeave`: 调用 `setHoveredElementId(null)`
- 与 GraphPanel 共享 `hoveredElementId` 状态

### 状态管理

使用 Zustand Store 中的以下状态：
- `regexString`: 正则表达式字符串
- `nodes`: 图表节点数据
- `hoveredElementId`: 当前悬停的元素ID
- `setHoveredElementId`: 设置悬停状态的方法
- `setRegexString`: 更新正则表达式的方法

## 使用方式

### 基本使用

1. **查看交互效果**
   - 在正则表达式输入框中输入或编辑正则表达式
   - 将鼠标悬停在不同的正则表达式片段上
   - 观察右侧图表中对应节点的高亮效果

2. **编辑正则表达式**
   - 点击右上角的"编辑"按钮
   - 在文本框中修改正则表达式
   - 点击"保存"确认修改，或"取消"放弃修改

### 视觉提示

- **蓝色高亮**: 表示可交互的正则表达式片段
- **脉冲指示器**: 提示用户可以进行交互
- **边框变化**: 悬停时的视觉反馈

## 代码示例

### 在页面中使用

```tsx
// src/app/page.tsx
import InteractiveRegexInput from "@panels/InteractiveRegexInput"

export default function Home() {
  return (
    <div className="h-[30%] min-h-[120px]">
      <InteractiveRegexInput />
    </div>
  )
}
```

### 组件接口

```tsx
// 组件不需要任何 props，直接从 Zustand Store 获取状态
<InteractiveRegexInput />
```

## 测试验证

### 手动测试步骤

1. 启动开发服务器：`npm run dev`
2. 访问 `http://localhost:3003`
3. 在正则表达式输入框中输入：`abc\d+`
4. 将鼠标悬停在 `abc` 片段上，观察右侧图表节点高亮
5. 将鼠标悬停在 `\d+` 片段上，观察对应节点高亮
6. 点击"编辑"按钮测试编辑功能

### 自动化测试

```bash
npm test -- InteractiveRegexInput.test.tsx
```

核心交互功能测试通过，验证了：
- 鼠标事件正确触发 `setHoveredElementId`
- 组件状态正确响应用户操作

## 性能优化

1. **useMemo 优化**: 字符串分割算法使用 `useMemo` 缓存结果
2. **事件防抖**: 避免频繁的状态更新
3. **智能匹配**: 优化的字符串匹配算法，避免重复计算

## 未来改进

1. **更精确的位置映射**: 基于 AST 节点的位置信息进行更精确的字符串分割
2. **语法高亮**: 为不同类型的正则表达式元素添加语法高亮
3. **错误提示**: 在输入无效正则表达式时提供实时错误提示
4. **快捷键支持**: 添加键盘快捷键支持编辑模式切换

## 总结

✅ **已完成的目标**：
- 创建了交互式输入框组件
- 实现了鼠标悬停高亮功能
- 替换了原来的简单 Textarea
- 提供了编辑和只读模式切换
- 添加了用户友好的视觉反馈

这个实现为用户提供了直观的正则表达式学习和调试体验，通过可视化的交互帮助用户理解正则表达式的结构和含义。
