<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长正则表达式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1e293b;
            color: #e2e8f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-case {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #334155;
            border-radius: 8px;
        }
        .regex-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            background-color: #475569;
            border: 1px solid #64748b;
            border-radius: 4px;
            color: #e2e8f0;
            font-family: monospace;
        }
        .copy-btn {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .copy-btn:hover {
            background-color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>长正则表达式测试用例</h1>
        <p>以下是一些可能导致节点文本溢出的长正则表达式，可以复制到主应用中测试：</p>
        
        <div class="test-case">
            <h3>测试用例 1: 长字符类</h3>
            <p>包含大量字符的字符类：</p>
            <input type="text" class="regex-input" readonly 
                   value="[abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-={}[]|\\:;\"'<>,.?/]">
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.value)">复制</button>
        </div>

        <div class="test-case">
            <h3>测试用例 2: 长量词表达式</h3>
            <p>包含复杂量词的表达式：</p>
            <input type="text" class="regex-input" readonly 
                   value="(verylongpatternwithmanycharsandnospaces){1,999999}">
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.value)">复制</button>
        </div>

        <div class="test-case">
            <h3>测试用例 3: 长序列</h3>
            <p>非常长的字符序列：</p>
            <input type="text" class="regex-input" readonly 
                   value="thisisaverylongsequenceofcharactersthatwillcauseoverflowifnothandledproperly">
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.value)">复制</button>
        </div>

        <div class="test-case">
            <h3>测试用例 4: 复杂嵌套表达式</h3>
            <p>包含多层嵌套的复杂表达式：</p>
            <input type="text" class="regex-input" readonly 
                   value="((([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})|([0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}))|(www\\.[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}))">
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.value)">复制</button>
        </div>

        <div class="test-case">
            <h3>测试用例 5: 中文长文本</h3>
            <p>包含中文字符的长文本：</p>
            <input type="text" class="regex-input" readonly 
                   value="这是一个包含很多中文字符的正则表达式测试用例用来验证中文字符的显示效果">
            <button class="copy-btn" onclick="copyToClipboard(this.previousElementSibling.value)">复制</button>
        </div>

        <div class="test-case">
            <h3>使用说明</h3>
            <ol>
                <li>点击"复制"按钮复制正则表达式</li>
                <li>在主应用的正则表达式输入框中粘贴</li>
                <li>观察节点是否正确处理长文本（不溢出容器）</li>
                <li>悬停在节点上查看完整的工具提示</li>
            </ol>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('已复制到剪贴板！');
            }, function(err) {
                console.error('复制失败: ', err);
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('已复制到剪贴板！');
            });
        }
    </script>
</body>
</html>
