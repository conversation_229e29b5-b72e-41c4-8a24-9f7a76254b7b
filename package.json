{"name": "regexai", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@types/lodash.debounce": "^4.0.9", "@xyflow/react": "^12.8.2", "adze": "^2.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "elkjs": "^0.10.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.525.0", "nanoid": "^5.1.5", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-resizable-panels": "^3.0.3", "regexp-tree": "^0.1.27", "tailwind-merge": "^3.3.1", "web-worker": "^1.5.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/ui": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.4.1", "jsdom": "^26.1.0", "node-fetch": "^3.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "vitest": "^3.2.4"}}