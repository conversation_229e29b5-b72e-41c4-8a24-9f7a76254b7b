import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable"
import { cn } from "@/lib/utils"

interface MainLayoutProps {
  leftPanel: React.ReactNode
  rightPanel: React.ReactNode
  className?: string
}

export default function MainLayout({ leftPanel, rightPanel, className }: MainLayoutProps) {
  return (
    <div className={cn("h-screen w-full bg-slate-900", className)}>
      <ResizablePanelGroup
        direction="horizontal"
        className="h-full w-full"
      >
        {/* 左侧面板 - 默认35%宽度 */}
        <ResizablePanel defaultSize={35} minSize={25} maxSize={50}>
          <div className="h-full w-full bg-slate-800 border-r border-slate-700 p-6">
            {leftPanel}
          </div>
        </ResizablePanel>

        {/* 分割线 */}
        <ResizableHandle className="w-1 bg-slate-700 hover:bg-blue-500 transition-colors duration-200" />

        {/* 右侧面板 - 剩余宽度 */}
        <ResizablePanel defaultSize={65} minSize={50}>
          <div className="h-full w-full bg-slate-800 p-6">
            {rightPanel}
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}
