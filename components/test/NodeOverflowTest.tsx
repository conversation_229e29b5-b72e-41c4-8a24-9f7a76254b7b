import React from 'react'
import { CustomNodeProps } from '@/types/graph'
import { NodeType } from '@/types/graph'

/**
 * 节点溢出测试组件
 * 用于验证长文本在节点中的显示效果
 */

// 模拟 CustomRegexNode 组件（用于测试）
function TestCustomRegexNode({ id, data }: CustomNodeProps) {
  return (
    <div
      className="px-3 py-2 rounded-lg border text-sm font-medium transition-all duration-200 bg-slate-800 text-slate-300 border-slate-600 min-w-[60px] max-w-[220px] m-2"
    >
      {/* 节点内容 */}
      <div 
        className="break-words overflow-hidden leading-tight"
        style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          wordBreak: 'break-all'
        }}
        title={data.label || data.content || data.originalText || `[${id}]`}
      >
        {data.label || data.content || data.originalText || `[${id}]`}
      </div>
    </div>
  )
}

// 模拟 SequenceNode 组件（用于测试）
function TestSequenceNode({ id, data }: CustomNodeProps) {
  return (
    <div
      className="bg-slate-800 border border-slate-700 rounded-lg p-3 hover:border-blue-500 transition-colors duration-200 min-w-[120px] max-w-[200px] m-2"
    >
      {/* 标题 */}
      <h3 className="font-semibold text-slate-50 mb-2 text-sm">
        序列
      </h3>

      {/* 内容 */}
      <div 
        className="font-mono text-slate-400 text-sm break-words overflow-hidden leading-tight"
        style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          wordBreak: 'break-all'
        }}
        title={data.content || data.label || '未知序列'}
      >
        {data.content || data.label || '未知序列'}
      </div>
    </div>
  )
}

/**
 * 节点溢出测试组件
 */
export default function NodeOverflowTest() {
  // 测试数据
  const testCases = [
    {
      id: 'short-text',
      label: '短文本',
      content: 'abc',
      description: '正常短文本'
    },
    {
      id: 'medium-text',
      label: '中等长度文本',
      content: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}',
      description: '中等长度的正则表达式'
    },
    {
      id: 'long-text',
      label: '超长文本',
      content: 'thisisaverylongsequenceofcharactersthatwillcauseoverflowifnothandledproperly',
      description: '超长文本，测试换行和截断'
    },
    {
      id: 'chinese-text',
      label: '中文文本',
      content: '这是一个包含很多中文字符的正则表达式测试用例用来验证中文字符的显示效果',
      description: '中文长文本测试'
    },
    {
      id: 'complex-regex',
      label: '复杂正则',
      content: '((([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})|([0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}))|(www\\.[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}))',
      description: '复杂嵌套正则表达式'
    }
  ]

  return (
    <div className="p-6 bg-slate-900 min-h-screen">
      <h1 className="text-2xl font-bold text-white mb-6">节点文本溢出修复测试</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-white mb-4">CustomRegexNode 测试</h2>
        <div className="flex flex-wrap">
          {testCases.map((testCase) => (
            <div key={`custom-${testCase.id}`} className="mb-4">
              <p className="text-sm text-slate-400 mb-2">{testCase.description}</p>
              <TestCustomRegexNode
                id={testCase.id}
                data={{
                  label: testCase.content,
                  content: testCase.content,
                  originalText: testCase.content,
                  semanticType: NodeType.LITERAL,
                  astNodeType: 'Test'
                }}
              />
            </div>
          ))}
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold text-white mb-4">SequenceNode 测试</h2>
        <div className="flex flex-wrap">
          {testCases.map((testCase) => (
            <div key={`sequence-${testCase.id}`} className="mb-4">
              <p className="text-sm text-slate-400 mb-2">{testCase.description}</p>
              <TestSequenceNode
                id={testCase.id}
                data={{
                  label: testCase.content,
                  content: testCase.content,
                  originalText: testCase.content,
                  semanticType: NodeType.LITERAL,
                  astNodeType: 'Test'
                }}
              />
            </div>
          ))}
        </div>
      </div>

      <div className="mt-8 p-4 bg-slate-800 rounded-lg">
        <h3 className="text-lg font-semibold text-white mb-2">测试说明</h3>
        <ul className="text-slate-300 space-y-1">
          <li>• 悬停在节点上查看完整的工具提示</li>
          <li>• 长文本应该在节点内换行或被截断，不会溢出</li>
          <li>• 节点应该保持在设定的最小和最大宽度范围内</li>
          <li>• 中文字符应该正确显示和换行</li>
        </ul>
      </div>
    </div>
  )
}
