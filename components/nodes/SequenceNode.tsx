import React, { memo } from 'react'
import type { CustomNodeProps } from '@/types/graph'

/**
 * 序列节点组件的 Props 接口
 * 扩展了 CustomNodeProps 以支持测试和无障碍访问
 */
interface SequenceNodeProps extends CustomNodeProps {
  'data-testid'?: string
}

/**
 * 序列节点组件
 * 用于显示 macro_sequence 类型的节点，表示连续的字符序列
 *
 * @param data - 节点数据，包含显示内容和标签
 * @param data-testid - 可选的测试标识符
 */
export const SequenceNode = memo(({ data, 'data-testid': testId }: SequenceNodeProps) => {
  return (
    <div
      data-testid={testId || 'sequence-node'}
      className="bg-slate-800 border border-slate-700 rounded-lg p-3 hover:border-blue-500 transition-colors duration-200 min-w-[120px] max-w-[200px]"
      role="article"
      aria-label="正则表达式序列节点"
    >
      {/* 标题 - 使用语义化标签和无障碍属性 */}
      <h3
        className="font-semibold text-slate-50 mb-2 text-sm"
        aria-label="序列节点标题"
      >
        序列
      </h3>

      {/* 内容 - 优化数据优先级，content 优先于 label */}
      <div
        className="font-mono text-slate-400 text-sm break-words overflow-hidden leading-tight"
        style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          wordBreak: 'break-all'
        }}
        title={data.content || data.label || '未知序列'}
      >
        {data.content || data.label || '未知序列'}
      </div>
    </div>
  )
})

// 设置组件显示名称，便于调试
SequenceNode.displayName = 'SequenceNode'

export default SequenceNode
