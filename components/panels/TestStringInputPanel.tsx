import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { EditableHighlightedText, MatchStats } from "@/components/ui/HighlightedText"
import type { MatchResult } from "@/lib/matcher"

interface TestStringInputPanelProps {
  value: string
  onChange: (value: string) => void
  matches?: MatchResult[]
  regexString?: string
  className?: string
  children?: React.ReactNode
  'aria-label'?: string
}

export default function TestStringInputPanel({
  value,
  onChange,
  matches = [],
  regexString = '',
  className,
  children,
  'aria-label': ariaLabel
}: TestStringInputPanelProps) {
  return (
    <Card className={cn("h-full bg-slate-800 border-slate-700", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-slate-50">
          测试字符串
        </CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col space-y-4">
        <div className="flex-1">
          <EditableHighlightedText
            value={value}
            onChange={onChange}
            matches={matches}
            regexString={regexString}
            placeholder="请输入测试字符串..."
            aria-label={ariaLabel}
            className="h-full"
          />
        </div>

        {/* 匹配统计信息 - 总是显示，让组件内部决定显示什么 */}
        <div className="border-t border-slate-600 pt-3">
          <MatchStats
            matches={matches}
            testString={value}
            regexString={regexString}
          />
        </div>

        {/* 渲染额外的子组件，比如匹配结果、高亮信息等 */}
        {children && (
          <div className="mt-auto">
            {children}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
