import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import InteractiveRegexInput from '../InteractiveRegexInput'

// Mock the useRegexStore hook
const mockSetHoveredElementId = vi.fn()
const mockSetRegexString = vi.fn()

vi.mock('@/hooks/useRegexStore', () => ({
  useRegexStore: () => ({
    regexString: 'abc\\d+',
    nodes: [
      {
        id: 'node-1',
        data: { content: 'abc', originalText: 'abc' }
      },
      {
        id: 'node-2', 
        data: { content: '\\d+', originalText: '\\d+' }
      }
    ],
    hoveredElementId: null,
    setHoveredElementId: mockSetHoveredElementId,
    setRegexString: mockSetRegexString
  })
}))

describe('InteractiveRegexInput', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the component with regex string', () => {
    render(<InteractiveRegexInput />)
    
    expect(screen.getByText('正则表达式')).toBeInTheDocument()
    expect(screen.getByText('abc')).toBeInTheDocument()
    expect(screen.getByText('\\d+')).toBeInTheDocument()
  })

  it('shows edit button and allows editing', () => {
    render(<InteractiveRegexInput />)
    
    const editButton = screen.getByText('编辑')
    expect(editButton).toBeInTheDocument()
    
    fireEvent.click(editButton)
    
    expect(screen.getByText('保存')).toBeInTheDocument()
    expect(screen.getByText('取消')).toBeInTheDocument()
  })

  it('calls setHoveredElementId on mouse enter/leave', () => {
    render(<InteractiveRegexInput />)
    
    const regexSegment = screen.getByText('abc')
    
    fireEvent.mouseEnter(regexSegment)
    expect(mockSetHoveredElementId).toHaveBeenCalledWith('node-1')
    
    fireEvent.mouseLeave(regexSegment)
    expect(mockSetHoveredElementId).toHaveBeenCalledWith(null)
  })

  it('shows interaction hint when regex has segments', () => {
    render(<InteractiveRegexInput />)
    
    expect(screen.getByText(/将鼠标悬停在正则表达式片段上/)).toBeInTheDocument()
  })
})
