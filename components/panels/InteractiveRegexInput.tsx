import React, { useMemo, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { useOptimizedRegexStore } from '@/hooks/useRegexStore'
import { Edit3, Eye } from 'lucide-react'

interface RegexSegment {
  text: string
  nodeId: string | null
  startIndex: number
  endIndex: number
}

/**
 * 交互式正则表达式输入框组件
 *
 * 功能：
 * - 将正则表达式字符串分割成对应图表节点的片段
 * - 支持鼠标悬停高亮对应的图表节点
 * - 支持编辑和只读模式切换
 */
export default function InteractiveRegexInput() {
  const { regexString, nodes, setHoveredElementId, setRegexString, hoveredElementId } = useOptimizedRegexStore()
  const [isEditMode, setIsEditMode] = useState(false)
  const [editValue, setEditValue] = useState('')

  /**
   * 将正则表达式字符串分割成对应节点的片段
   * 改进版本：优先选择最小粒度的节点，避免大节点覆盖小节点
   */
  const regexSegments = useMemo((): RegexSegment[] => {
    if (!regexString || !nodes || nodes.length === 0) {
      return [{
        text: regexString || '',
        nodeId: null,
        startIndex: 0,
        endIndex: regexString?.length || 0
      }]
    }

    // 过滤掉占位符节点
    const validNodes = nodes.filter(node =>
      node.id !== 'empty-placeholder' &&
      node.id !== 'error-placeholder' &&
      node.data?.content &&
      node.data.content.trim().length > 0
    )

    if (validNodes.length === 0) {
      return [{
        text: regexString,
        nodeId: null,
        startIndex: 0,
        endIndex: regexString.length
      }]
    }

    // 创建字符映射数组
    const charToNodeMap: (string | null)[] = new Array(regexString.length).fill(null)

    // 按内容长度从短到长排序，优先匹配小粒度的节点
    const sortedNodes = validNodes
      .map(node => ({
        id: node.id,
        content: node.data?.content || '',
        length: (node.data?.content || '').length
      }))
      .filter(node => node.content.length > 0)
      .sort((a, b) => a.length - b.length)

    // 逐个匹配节点，短的优先
    for (const node of sortedNodes) {
      const content = node.content
      let searchIndex = 0

      while (searchIndex < regexString.length) {
        const matchIndex = regexString.indexOf(content, searchIndex)
        if (matchIndex === -1) break

        const matchEnd = matchIndex + content.length

        // 检查这个范围是否已经被占用
        let canUse = true
        for (let i = matchIndex; i < matchEnd; i++) {
          if (charToNodeMap[i] !== null) {
            canUse = false
            break
          }
        }

        if (canUse) {
          // 标记这些字符
          for (let i = matchIndex; i < matchEnd; i++) {
            charToNodeMap[i] = node.id
          }
          // 找到一个匹配就停止，避免重复匹配
          break
        }

        searchIndex = matchIndex + 1
      }
    }

    // 根据字符映射生成片段
    const segments: RegexSegment[] = []
    let currentSegmentStart = 0
    let currentNodeId = charToNodeMap[0]

    for (let i = 1; i <= regexString.length; i++) {
      const nextNodeId = i < regexString.length ? charToNodeMap[i] : null

      // 如果节点ID发生变化或到达字符串末尾，结束当前片段
      if (nextNodeId !== currentNodeId || i === regexString.length) {
        segments.push({
          text: regexString.slice(currentSegmentStart, i),
          nodeId: currentNodeId,
          startIndex: currentSegmentStart,
          endIndex: i
        })

        currentSegmentStart = i
        currentNodeId = nextNodeId
      }
    }

    return segments.length > 0 ? segments : [{
      text: regexString,
      nodeId: null,
      startIndex: 0,
      endIndex: regexString.length
    }]
  }, [regexString, nodes])

  /**
   * 处理鼠标进入事件
   */
  const handleMouseEnter = (nodeId: string | null) => {
    if (nodeId) {
      setHoveredElementId(nodeId)
    }
  }

  /**
   * 处理鼠标离开事件
   */
  const handleMouseLeave = () => {
    setHoveredElementId(null)
  }

  /**
   * 切换到编辑模式
   */
  const handleEditClick = () => {
    setEditValue(regexString)
    setIsEditMode(true)
  }

  /**
   * 保存编辑内容
   */
  const handleSaveEdit = () => {
    setRegexString(editValue)
    setIsEditMode(false)
  }

  /**
   * 取消编辑
   */
  const handleCancelEdit = () => {
    setEditValue('')
    setIsEditMode(false)
  }

  return (
    <Card className="h-full bg-slate-800 border-slate-700">
      <CardHeader className="pb-4 flex flex-row items-center justify-between space-y-0">
        <CardTitle className="text-lg font-semibold text-slate-50">
          正则表达式
        </CardTitle>
        <div className="flex items-center space-x-2">
          {isEditMode ? (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancelEdit}
                className="h-8 px-3 text-xs bg-slate-700 border-slate-600 text-slate-300 hover:bg-slate-600"
              >
                取消
              </Button>
              <Button
                size="sm"
                onClick={handleSaveEdit}
                className="h-8 px-3 text-xs bg-blue-600 hover:bg-blue-700 text-white"
              >
                保存
              </Button>
            </>
          ) : (
            <Button
              size="sm"
              variant="outline"
              onClick={handleEditClick}
              className="h-8 px-3 text-xs bg-slate-700 border-slate-600 text-slate-300 hover:bg-slate-600"
            >
              <Edit3 className="w-3 h-3 mr-1" />
              编辑
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col space-y-4">
        <div className="flex-1 min-h-[120px] bg-slate-900 border border-slate-600 rounded-lg p-3 overflow-auto">
          {isEditMode ? (
            <Textarea
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              placeholder="请输入正则表达式..."
              className="w-full h-full min-h-[100px] bg-transparent border-none text-slate-300 placeholder:text-slate-500 focus:ring-0 focus:outline-none resize-none font-mono text-sm"
              autoFocus
            />
          ) : regexString ? (
            <div className="font-mono text-slate-300 text-sm leading-relaxed whitespace-pre-wrap break-all">
              {regexSegments.map((segment, index) => {
                const isHighlighted = segment.nodeId && hoveredElementId === segment.nodeId

                return (
                  <span
                    key={`${segment.nodeId || 'null'}-${index}-${segment.startIndex}`}
                    className={cn(
                      "transition-all duration-200",
                      segment.nodeId ? [
                        "cursor-pointer",
                        "rounded px-1 py-0.5 mx-0.5",
                        "border border-transparent",
                        // 悬停样式
                        "hover:bg-blue-500/20",
                        "hover:text-blue-300",
                        "hover:border-blue-500/30",
                        "hover:shadow-sm",
                        // 高亮样式（当对应的图表节点被悬停时）
                        isHighlighted && [
                          "bg-blue-500/30",
                          "text-blue-100",
                          "border-blue-500/50",
                          "shadow-md shadow-blue-500/20",
                          "ring-1 ring-blue-500/30"
                        ]
                      ] : [
                        "text-slate-400",
                        // 非交互元素的微弱样式
                        "opacity-70"
                      ]
                    )}
                    onMouseEnter={() => handleMouseEnter(segment.nodeId)}
                    onMouseLeave={handleMouseLeave}
                    title={segment.nodeId ? `节点: ${segment.nodeId}` : undefined}
                  >
                    {segment.text}
                  </span>
                )
              })}
            </div>
          ) : (
            <div className="text-slate-500 text-sm italic">
              请输入正则表达式...
            </div>
          )}
        </div>

        {/* 交互提示 */}
        {!isEditMode && regexString && regexSegments.some(s => s.nodeId) && (
          <div className="text-xs text-slate-400 flex items-center space-x-2 px-2">
            <div className="w-2 h-2 bg-blue-500/50 rounded-full animate-pulse"></div>
            <span>将鼠标悬停在正则表达式片段上，查看对应的图表节点高亮</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
