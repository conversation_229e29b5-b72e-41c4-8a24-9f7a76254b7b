import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"

interface RegexInputPanelProps {
  value: string
  onChange: (value: string) => void
  className?: string
  children?: React.ReactNode
  'aria-label'?: string
}

export default function RegexInputPanel({
  value,
  onChange,
  className,
  children,
  'aria-label': ariaLabel
}: RegexInputPanelProps) {
  return (
    <Card className={cn("h-full bg-slate-800 border-slate-700", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-slate-50">
          正则表达式
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col space-y-4">
        <Textarea
          value={value}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(e.target.value)}
          placeholder="请输入正则表达式..."
          aria-label={ariaLabel}
          className="flex-1 min-h-[120px] bg-slate-900 border-slate-600 text-slate-300 placeholder:text-slate-500 focus:border-blue-500 focus:ring-blue-500/20 resize-none rounded-lg"
        />

        {/* 渲染额外的子组件，比如错误提示、帮助信息等 */}
        {children && (
          <div className="mt-auto">
            {children}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
