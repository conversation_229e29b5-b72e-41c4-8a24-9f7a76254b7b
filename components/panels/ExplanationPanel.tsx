"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useRegexStore } from "@/store/regex.store"
import { useTranslation } from "@/lib/i18n"
import { AlertCircle, Lightbulb, RefreshCw } from "lucide-react"
import { useMemo } from "react"

interface ExplanationPanelProps {
  className?: string
}

/**
 * ExplanationPanel 组件 - AI 解释面板
 * 
 * 功能：
 * - 从 Store 中获取 aiExplanations 和 hoveredElementId
 * - 显示当前悬停节点的 AI 解释文本
 * - 提供加载状态和错误处理
 * - 支持无悬停状态的友好提示
 */
export default function ExplanationPanel({ className }: ExplanationPanelProps) {
  const { t } = useTranslation()

  // 修复：使用分离的 selector 避免无限循环，直接获取需要的状态值
  const hoveredElementId = useRegexStore(state => state.hoveredElementId)
  const aiExplanations = useRegexStore(state => state.aiExplanations)
  const pendingNodeIds = useRegexStore(state => state.pendingNodeIds)
  const aiErrorNodeIds = useRegexStore(state => state.aiErrorNodeIds)
  const flowNodes = useRegexStore(state => state.flowNodes)
  const retryExplanation = useRegexStore(state => state.retryExplanationForNode)

  // 优化：使用useMemo缓存节点查找结果，只在hoveredElementId或flowNodes变化时重新计算
  const hoveredNode = useMemo(() =>
    hoveredElementId ? flowNodes.find(node => node.id === hoveredElementId) : null,
    [hoveredElementId, flowNodes]
  )

  const nodeContent = hoveredNode?.data?.regexFragment || hoveredNode?.data?.label || hoveredElementId

  // 优化：使用useMemo缓存加载状态检查
  const isLoading = useMemo(() => {
    if (!hoveredElementId) return false
    for (const nodeSet of pendingNodeIds.values()) {
      if (nodeSet.has(hoveredElementId)) {
        return true
      }
    }
    return false
  }, [hoveredElementId, pendingNodeIds])

  // 优化：使用useMemo缓存解释查找
  const currentExplanation = useMemo(() => {
    if (!hoveredElementId) return null
    const cacheKey = (typeof nodeContent === 'string' && nodeContent) ? `explanation_${nodeContent.replace(/\s+/g, '_')}` : hoveredElementId
    return aiExplanations.get(cacheKey) || aiExplanations.get(hoveredElementId) || null
  }, [hoveredElementId, nodeContent, aiExplanations])

  const hasError = hoveredElementId ? aiErrorNodeIds.has(hoveredElementId) : false
  
  // 渲染内容
  const renderContent = () => {
    // 没有悬停元素时的默认状态
    if (!hoveredElementId) {
      return (
        <div className="flex flex-col items-center justify-center h-32 text-center">
          <Lightbulb className="w-8 h-8 text-slate-400 mb-2" />
          <p className="text-slate-400 text-sm">
            {t('explanation.hoverToSeeExplanation')}
          </p>
        </div>
      )
    }
    
    // 正在加载状态
    if (isLoading) {
      return (
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <span className="text-sm text-slate-400">
              {t('explanation.loadingExplanation')}
            </span>
          </div>
          <Skeleton className="h-4 w-full bg-slate-600/60" />
          <Skeleton className="h-4 w-3/4 bg-slate-600/60" />
          <Skeleton className="h-4 w-1/2 bg-slate-600/60" />
        </div>
      )
    }
    
    // 检查是否有错误状态 - 优化：使用统一的错误状态判断
    if (hasError) {
      const handleRetry = () => {
        if (hoveredElementId && nodeContent && typeof nodeContent === 'string') {
          retryExplanation(hoveredElementId, nodeContent);
        }
      };

      return (
        <div className="space-y-3">
          <div className="flex items-start space-x-2 p-3 bg-red-900/20 border border-red-800 rounded-lg">
            <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm text-red-300 mb-2">
                {currentExplanation || t('errors.aiExplanationFailed')}
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={handleRetry}
                className="h-7 px-2 text-xs border-red-700 text-red-300 hover:bg-red-900/30"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                {t('explanation.retry')}
              </Button>
            </div>
          </div>
        </div>
      )
    }

    // 有解释内容时显示
    if (currentExplanation && typeof currentExplanation === 'string') {
      
      return (
        <div className="space-y-2">
          <div className="flex items-center space-x-2 mb-3">
            <Lightbulb className="w-4 h-4 text-blue-400" />
            <span className="text-sm font-medium text-blue-400">
              {t('explanation.aiExplanation')}
            </span>
          </div>
          <div className="text-sm text-slate-300 leading-relaxed">
            {currentExplanation}
          </div>
        </div>
      )
    }
    
    // 没有解释但有悬停元素（可能还未请求或请求失败）
    return (
      <div className="flex flex-col items-center justify-center h-32 text-center">
        <AlertCircle className="w-8 h-8 text-slate-400 mb-2" />
        <p className="text-slate-400 text-sm">
          {t('explanation.noExplanationAvailable')}
        </p>
      </div>
    )
  }
  
  return (
    <Card className={cn("h-full bg-slate-800 border-slate-700", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold text-slate-50 flex items-center space-x-2">
          <Lightbulb className="w-5 h-5" />
          <span>{t('explanation.title')}</span>
        </CardTitle>
        {hoveredElementId && (
          <p className="text-xs text-slate-400">
            {t('explanation.currentElement')}: <code className="bg-slate-700 px-1 rounded">{hoveredElementId}</code>
          </p>
        )}
      </CardHeader>
      <CardContent className="pt-0 overflow-auto">
        {renderContent()}
      </CardContent>
    </Card>
  )
}
