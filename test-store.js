// 简单的测试脚本来验证 store 功能
// 在浏览器控制台中运行

// 测试 parseRegex 函数
async function testParseRegex() {
  console.log('=== 测试 parseRegex 函数 ===');
  
  // 动态导入
  const { parseRegex } = await import('./src/lib/parser.js');
  
  // 测试简单正则表达式
  const result1 = parseRegex('abc');
  console.log('解析 "abc":', result1);
  
  // 测试复杂正则表达式
  const result2 = parseRegex('(a+|b*){2,5}[cde]?\\d+');
  console.log('解析复杂正则:', result2);
  
  // 测试错误情况
  const result3 = parseRegex('[');
  console.log('解析错误正则:', result3);
}

// 测试 getLayoutedElements 函数
async function testLayoutedElements() {
  console.log('=== 测试 getLayoutedElements 函数 ===');
  
  // 动态导入
  const { parseRegex } = await import('./src/lib/parser.js');
  const { getLayoutedElements } = await import('./src/lib/layout-client.js');
  
  // 解析正则表达式
  const parseResult = parseRegex('abc');
  if (parseResult.ast) {
    try {
      const layoutResult = await getLayoutedElements(parseResult.ast);
      console.log('布局结果:', layoutResult);
      console.log('节点数量:', layoutResult.nodes.length);
      console.log('边数量:', layoutResult.edges.length);
    } catch (error) {
      console.error('布局计算失败:', error);
    }
  }
}

// 测试 store
async function testStore() {
  console.log('=== 测试 Store 功能 ===');
  
  // 这个需要在 React 组件中测试
  console.log('Store 测试需要在 React 组件中进行');
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.testParseRegex = testParseRegex;
  window.testLayoutedElements = testLayoutedElements;
  window.testStore = testStore;
  
  console.log('测试函数已加载到 window 对象:');
  console.log('- testParseRegex()');
  console.log('- testLayoutedElements()');
  console.log('- testStore()');
}
