import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 实验性功能配置
  experimental: {
    // 允许外部目录导入
    externalDir: true,
  },

  // 暂时忽略构建错误以便开发
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // 简化的 webpack 配置
  webpack: (config, { isServer, webpack }) => {
    // 只在客户端处理 web-worker 问题
    if (!isServer) {
      // 使用 DefinePlugin 替换 require.resolve 调用
      config.plugins.push(
        new webpack.DefinePlugin({
          'require.resolve': '(function(id) { throw new Error("Module not found: " + id); })',
        })
      );

      config.resolve.fallback = {
        ...config.resolve.fallback,
        'web-worker': false,
      };
    }

    return config;
  },
};

export default nextConfig;
