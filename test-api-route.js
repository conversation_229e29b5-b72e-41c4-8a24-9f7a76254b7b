/**
 * 测试 Next.js API 路由 /api/explain
 * 运行方式: 
 * 1. 启动开发服务器: npm run dev
 * 2. 在另一个终端运行: node test-api-route.js
 */

// 使用 Node.js 18+ 内置的 fetch
// 如果是较老版本的 Node.js，请安装 node-fetch: npm install node-fetch

async function testHealthEndpoint(healthEndpoint) {
  console.log('🏥 测试健康检查端点...');

  try {
    // 测试 GET /api/health
    console.log('\n🔍 测试: GET /api/health');
    const response = await fetch(healthEndpoint);
    const data = await response.json();

    console.log(`📊 状态: ${response.status} ${response.statusText}`);
    console.log('🏥 健康状态:', data.status);
    console.log('⏱️  运行时间:', data.uptime, '秒');
    console.log('📦 版本:', data.version);
    console.log('🌍 环境:', data.environment);
    console.log('🔧 服务状态:');
    console.log('  - OpenRouter:', data.services.openrouter);
    console.log('  - Database:', data.services.database);

    if (response.ok) {
      console.log('✅ 健康检查通过');
    } else {
      console.log('⚠️  健康检查失败');
    }

    // 测试 HEAD /api/health
    console.log('\n🔍 测试: HEAD /api/health');
    const headResponse = await fetch(healthEndpoint, { method: 'HEAD' });
    console.log(`📊 HEAD 状态: ${headResponse.status}`);

    const headText = await headResponse.text();
    console.log('📄 HEAD 响应体长度:', headText.length, '(应该为0)');

  } catch (error) {
    console.error('❌ 健康检查错误:', error.message);
  }
}

async function testAPIRoute() {
  console.log('🧪 测试 Next.js API 路由...\n');

  const baseUrl = 'http://localhost:3000';
  const explainEndpoint = `${baseUrl}/api/explain`;
  const healthEndpoint = `${baseUrl}/api/health`;

  // 首先测试健康检查
  await testHealthEndpoint(healthEndpoint);

  // 测试用例
  const testCases = [
    {
      name: '基本正则片段',
      snippet: '\\d+',
      description: '匹配一个或多个数字'
    },
    {
      name: '字符类',
      snippet: '[a-zA-Z]',
      description: '匹配单个字母'
    },
    {
      name: '量词',
      snippet: '.*',
      description: '匹配任意字符零次或多次'
    },
    {
      name: '边界断言',
      snippet: '^start',
      description: '匹配行开头的start'
    }
  ];

  console.log('\n📋 测试 /api/explain 端点...');

  for (const testCase of testCases) {
    console.log(`\n🔍 测试: ${testCase.name}`);
    console.log(`📝 片段: ${testCase.snippet}`);
    console.log(`📋 期望: ${testCase.description}`);

    try {
      const response = await fetch(explainEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          snippet: testCase.snippet
        })
      });

      console.log(`📊 状态: ${response.status} ${response.statusText}`);

      const data = await response.json();

      if (response.ok) {
        console.log('✅ 请求成功');
        console.log('📖 AI 解释:');
        console.log('─'.repeat(40));
        console.log(data.explanation);
        console.log('─'.repeat(40));
      } else {
        console.log('❌ 请求失败');
        console.log('🔍 错误信息:', data.error);
        console.log('🏷️  错误代码:', data.code);
      }

    } catch (error) {
      console.error('❌ 网络错误:', error.message);
    }

    // 添加延迟避免速率限制
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // 测试错误情况
  console.log('\n🚨 测试错误情况...');

  // 测试空请求体
  try {
    console.log('\n🔍 测试: 空请求体');
    const response = await fetch(explainEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });

    const data = await response.json();
    console.log(`📊 状态: ${response.status}`);
    console.log('🔍 错误信息:', data.error);
    console.log('🏷️  错误代码:', data.code);
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }

  // 测试无效 JSON
  try {
    console.log('\n🔍 测试: 无效 JSON');
    const response = await fetch(explainEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: 'invalid json'
    });

    const data = await response.json();
    console.log(`📊 状态: ${response.status}`);
    console.log('🔍 错误信息:', data.error);
    console.log('🏷️  错误代码:', data.code);
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }

  // 测试过大的请求
  try {
    console.log('\n🔍 测试: 过大的请求');
    const largeSnippet = 'a'.repeat(1000); // 创建一个很大的片段
    const response = await fetch(explainEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ snippet: largeSnippet })
    });

    const data = await response.json();
    console.log(`📊 状态: ${response.status}`);
    console.log('🔍 错误信息:', data.error);
    console.log('🏷️  错误代码:', data.code);
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }

  // 测试 CORS（如果可能）
  try {
    console.log('\n🔍 测试: CORS 检查');
    const response = await fetch(explainEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://malicious.site' // 模拟恶意来源
      },
      body: JSON.stringify({ snippet: '\\d+' })
    });

    const data = await response.json();
    console.log(`📊 状态: ${response.status}`);
    if (!response.ok) {
      console.log('🔍 错误信息:', data.error);
      console.log('🏷️  错误代码:', data.code);
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
  }

  console.log('\n✨ 测试完成!');
}

// 运行测试
testAPIRoute();
