/// <reference types="vitest" />
import { defineConfig } from 'vite'
import path from 'path'

export default defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/test/setup.ts'],
    // 测试超时配置（可通过环境变量配置）
    testTimeout: parseInt(process.env.TEST_TIMEOUT_MS || '10000', 10),
    hookTimeout: parseInt(process.env.TEST_TIMEOUT_MS || '10000', 10),
    // 删除 pool 配置，使用默认的单线程模式
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
